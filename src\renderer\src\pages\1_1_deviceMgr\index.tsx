import React from 'react';

import {
    <PERSON><PERSON>,
    <PERSON>dal<PERSON><PERSON>lay,
    <PERSON>dal<PERSON>ontent,
    <PERSON>dal<PERSON>eader,
    <PERSON>dal<PERSON>ooter,
    ModalBody,
    ModalCloseButton
} from '@chakra-ui/react';

import { <PERSON><PERSON>, <PERSON>b<PERSON>ist, TabPanels, TabPanel, Tab } from '@chakra-ui/react';
import { Button, HStack, VStack } from '@chakra-ui/react';
import { Icon, useToast } from '@chakra-ui/react';
import { InfoIcon } from '@chakra-ui/icons';

import { styles } from '@renderer/utils/theme';
import {
    DeviceSelector,
    // SerialSettings,
    FilterTable
    // AddFilterModal
} from '@components/deviceManager';

// import { RestoreFilterDialog, DeleteFilterDialog } from '@components/deviceManager/Dialogs';

import { useDeviceSettings } from './hooks/useDeviceSettings';
// import { useSerialConfig } from './hooks/useSerialConfig';
// import { useFilterManager } from './hooks/useFilterManager';

// import { DeviceConfig } from '@shared/types';
import { DeviceModel } from '@shared/commondefines';
import logger from '@renderer/utils/logger';

interface DeviceManagerProps {
    isOpen: boolean;
    onClose: () => void;
}

const DeviceManager: React.FC<DeviceManagerProps> = ({ isOpen, onClose }) => {
    const toast = useToast();

    const deviceSettings = useDeviceSettings(isOpen);
    // const serialConfig = useSerialConfig(isOpen);
    // const filterManager = useFilterManager(deviceSettings.defaultDevice);

    const handleConfirm = async (): Promise<void> => {
        try {
            if (deviceSettings.defaultDevice === DeviceModel.unknown) {
                toast({
                    title: '设备型号不能为空',
                    status: 'warning',
                    duration: 3000,
                    isClosable: true,
                    position: 'top'
                });
                return;
            }

            if (deviceSettings.serialConfig.path === '') {
                toast({
                    title: '通信端口不能为空',
                    status: 'warning',
                    duration: 3000,
                    isClosable: true,
                    position: 'top'
                });
                return;
            }

            logger.info('handleConfirm deviceSettings', {
                data: deviceSettings.serialConfig,
                component: './src/renderer/src/pages/1_1_deviceMgr/index.tsx'
            });

            // 保存所有配置
            if (!deviceSettings.saveDeviceSettings()) {
                throw new Error('保存设备设置失败');
            }

            // await window.customApi.app.updateAppSettings({
            //     deviceModel: deviceSettings.selectedDevice
            // });

            // if (!(await serialConfig.saveConfig())) {
            //     throw new Error('通讯参数保存失败');
            // }

            // if (!(await filterManager.saveFilters())) {
            //     throw new Error('滤光片参数保存失败');
            // }

            toast({
                title: '保存成功',
                status: 'success',
                duration: 2000
            });
            onClose();
        } catch (error) {
            logger.error('保存配置失败', error, {
                component: './src/renderer/src/pages/1_1_deviceMgr/index.tsx'
            });
            toast({
                title: String(error),
                status: 'error',
                duration: 3000,
                isClosable: true
            });
        }
    };

    return (
        <>
            <Modal
                isOpen={isOpen}
                onClose={onClose}
                size="xl"
                isCentered
                closeOnEsc={false}
                closeOnOverlayClick={false}
            >
                <ModalOverlay />
                <ModalContent maxW="600px" maxH="650px" w="100vw" h="100vh" overflow="auto">
                    <ModalHeader sx={styles.modalHeader}>
                        <HStack alignItems="center" gap={6}>
                            <Icon as={InfoIcon} boxSize={6} />
                            <span>设备管理</span>
                        </HStack>
                        <ModalCloseButton
                            color="white"
                            bg="rgba(255,255,255,0.1)"
                            _hover={{ bg: 'rgba(255,255,255,0.2)' }}
                            borderRadius="full"
                            size="lg"
                        />
                    </ModalHeader>

                    <ModalBody>
                        <Tabs>
                            <TabList>
                                <Tab sx={styles.tab}>仪器设置</Tab>
                                {/* <Tab fontFamily="MiSans-Bold">通讯参数</Tab> */}
                                <Tab sx={styles.tab}>滤光片参数</Tab>
                            </TabList>
                            <TabPanels>
                                <TabPanel>
                                    <VStack spacing={4} align={'stretch'}>
                                        <DeviceSelector {...deviceSettings} />
                                        {/* <SerialSettings
                                            config={serialConfig.config}
                                            onChange={serialConfig.updateConfig}
                                            onTest={() => console.log('Testing connection...')}
                                        /> */}
                                    </VStack>
                                </TabPanel>

                                <TabPanel>
                                    <FilterTable
                                        filters={
                                            deviceSettings.devicesList.find(
                                                (device) =>
                                                    device.model === deviceSettings.defaultDevice
                                            )?.filters ?? []
                                        }
                                        // filters={filterManager.filters}
                                        // selectedFilters={filterManager.selectedFilters}
                                        // maxFilters={deviceSettings.devicesList[0].filters.length}
                                        // onSelect={filterManager.toggleFilterSelection}
                                        // onAdd={() => filterManager.handleAddClick()} // 使用新的处理函数
                                        // onDelete={filterManager.handleDeleteFilters}
                                        // onRestore={filterManager.handleRestoreDefaults}
                                    />
                                </TabPanel>
                            </TabPanels>
                        </Tabs>
                    </ModalBody>
                    <ModalFooter gap={6} display="flex" justifyContent="flex-end">
                        <Button colorScheme="blue" mr={3} onClick={handleConfirm} size={'lg'}>
                            确定
                        </Button>
                        <Button onClick={onClose} size={'lg'} colorScheme="blue">
                            退出
                        </Button>
                    </ModalFooter>
                </ModalContent>
            </Modal>
            {/* {filterManager.isAddModalOpen && (
                <AddFilterModal
                    isOpen={filterManager.isAddModalOpen}
                    onClose={() => filterManager.setIsAddModalOpen(false)}
                    availableNumbers={filterManager.getAvailableNumbers()}
                    filterData={filterManager.newFilter}
                    onFilterChange={filterManager.setNewFilter}
                    onSubmit={filterManager.handleAddFilter}
                />
            )} */}

            {/* 添加删除确认对话框 */}
            {/* <DeleteFilterDialog
                isOpen={filterManager.isDeleteDialogOpen}
                onClose={() => filterManager.setIsDeleteDialogOpen(false)}
                onConfirm={filterManager.handleDeleteConfirm}
                count={filterManager.selectedFilters.length}
            /> */}

            {/* 添加恢复默认对话框 */}
            {/* <RestoreFilterDialog
                isOpen={filterManager.isRestoreDialogOpen}
                onClose={() => filterManager.setIsRestoreDialogOpen(false)}
                onConfirm={filterManager.handleRestoreConfirm}
            /> */}

            {/* <AlertDialogs
                isDeleteOpen={false} // TODO: 添加状态管理
                isRestoreOpen={false}
                isMaxLimitOpen={false}
                selectedCount={filterManager.selectedFilters.length}
                maxFilters={deviceSettings.currentDevice?.filters.length}
                onDeleteConfirm={filterManager.handleDeleteFilters}
                onRestoreConfirm={filterManager.handleRestoreDefaults}
                onDeleteClose={() => {}} // TODO: 添加处理函数
                onRestoreClose={() => {}}
                onMaxLimitClose={() => {}}
            /> */}
        </>
    );
};

export default DeviceManager;
