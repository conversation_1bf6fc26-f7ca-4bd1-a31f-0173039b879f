import { TestRecord, PrintRequest } from '@shared/types';

// 模板接口
interface Template {
    id: string;
    name: string;
    projectCode: string;
    html: string;
    language: 'zh' | 'en';
}

// 模板引擎类
export class ReportTemplateEngine {
    private templates: Map<string, Template> = new Map();

    constructor() {
        this.initializeDefaultTemplates();
    }

    // 初始化默认模板
    private initializeDefaultTemplates() {
        // 默认中文模板
        const defaultZhTemplate: Template = {
            id: 'default-zh',
            name: '默认中文模板',
            projectCode: 'default',
            language: 'zh',
            html: this.getDefaultZhTemplate()
        };

        // 默认英文模板
        const defaultEnTemplate: Template = {
            id: 'default-en',
            name: 'Default English Template',
            projectCode: 'default',
            language: 'en',
            html: this.getDefaultEnTemplate()
        };

        this.templates.set('default-zh', defaultZhTemplate);
        this.templates.set('default-en', defaultEnTemplate);
    }

    // 获取默认中文模板
    private getDefaultZhTemplate(): string {
        return `
 <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>{{reportTitle}}</title>
            <style>
                @page {
                    size: A4;
                    margin: 10mm 15mm 10mm 15mm;
                }
                body {
                    font-family: 'MiSans-Normal','MiSans-Regular','Microsoft YaHei', 'SimSun', Arial, sans-serif;
                    margin: 0;
                    padding: 0;
                    background: #f4f8fb;
                }
                .report-bg {
                    min-height: 100vh;
                    display: flex;
                    justify-content: center;
                    align-items: flex-start;
                    padding: 48px 0;
                    background: linear-gradient(90deg, #f4f8fb 60%, #eaf3fa 100%);
                }
                .container {
                    width: 100%;
                    max-width: 980px;
                    margin: 0 32px;
                    background: #fff;
                    border-radius: 16px;
                    box-shadow: 0 6px 32px rgba(25, 118, 210, 0.10), 0 1.5px 6px rgba(25, 118, 210, 0.06);
                    padding: 40px 56px 28px 56px;
                    border: 1.5px solid #e3eaf5;
                }
                .report-title {
                    text-align: center;
                    font-size: 30px;
                    font-weight: bold;
                    color: #1976d2;
                    margin-bottom: 4px;
                    letter-spacing: 2px;
                }
                .report-subtitle {
                    text-align: center;
                    font-size: 18px;
                    font-weight: bold;
                    color: #1565c0;
                    margin-top:18px;
                    margin-bottom: 32px;
                }
                .divider {
                    width: 100%;
                    height: 2px;
                    background: #1976d2;
                    border-radius: 2px;
                    margin: 0 auto 22px auto;
                }
                .info-table {
                    width: 95%;
                    border-collapse: separate;
                    border-spacing: 0 2px;
                    margin: 0 auto 14px auto;
                    font-size: 14px;
                    table-layout: fixed;
                }
                .info-table td {
                    padding: 5px 2px 5px 2px;
                    border: none;
                }
                .info-table .label {
                    color: #1976d2;
                    font-weight: bold;
                    text-align: right;
                    width: 100px;
                    white-space: nowrap;
                  
                }
                .info-table .value {
                    color: #222;
                    text-align: left;
                    width: 210px;
                     font-size: 13px;
    
                }
                .plate-section {
                    margin-top: 18px;
                }
                .plate-table {
                    width: 100%;
                    border-collapse: collapse;
                    margin: 0 0 10px 0;
                    font-size: 13px;
                    background: #fafdff;
                    border-radius: 8px;
                    overflow: hidden;
                    table-layout: fixed;
                }
                .plate-table th, .plate-table td {
                    border: 1px solid #c7d7ea;
                    padding: 2px 2px;
                    text-align: center;
                    height: 32px;
                    line-height: 1.4;
                    width: 70px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    font-size: 12px;
                }
                .plate-table th {
                    background: #e3f0fc;
                    font-weight: bold;
                    color: #1976d2;
                }
                .plate-table tr th:first-child,
                .plate-table tr td:first-child {
                    background: #f4f8fb;
                    font-weight: bold;
                    color: #1976d2;
                    width: 45px;
                }
                .note {
                    margin-top: 18px;
                    margin-bottom:18px;
                    font-size: 13px;
                    color: #4a5a6a;
                    background:rgb(255, 255, 255);
                    border-radius: 6px;
                    padding: 8px 16px;
                }
                .note p {
                    margin: 2px 0;
                }
                .sign-area {
                    margin-top: 32px;
                    display: flex;
                    justify-content: flex-end;
                    font-size: 15px;
                    color: #1976d2;
                }
                .sign-area span {
                    margin-left: 100px;
                }
                @media print {
                    body { background: #fff; }
                    .report-bg { background: #fff; padding: 0; }
                    .container { box-shadow:none; border-radius: 0; margin: 0; padding: 12mm 0 8mm 0; }
                }
            </style>
        </head>
        <body>
            <div class="report-bg">
            <div class="container">
                <div class="report-title">{{reportTitle}}</div>
                <div class="report-subtitle">{{reportSubtitle}}</div>
               
                <div class="divider"></div>
              
                <table class="info-table">
                    <tr>
                       <td class="label">微孔板号:</td><td class="value">{{mtpNumber}}</td>
                       <td class="label">阳性公式:</td><td class="value">{{positiveFormula}}</td>
                       <td class="label">试剂批号:</td><td class="value">{{reagentBatch}}</td>
                      
                    </tr>
                    <tr>
                       <td class="label">检测仪器:</td><td class="value">{{testInstrument}}</td>
                       <td class="label">Cutoff值:</td><td class="value">{{cutoffValue}}</td>
                       <td class="label">试剂效期:</td><td class="value">{{reagentExpiry}}</td>
                      
                      
                    </tr>
                    <tr>
                       <td class="label">检测项目:</td><td class="value">{{projectName}}</td>
                       <td class="label">样品来源:</td><td class="value">{{sampleSource}}</td>
                       <td class="label">试剂厂商:</td><td class="value">{{reagentSupplier}}</td>
                       
                       
                    </tr>
                    <tr>
                        
                       <td class="label">检测波长:</td><td class="value">{{testWave}}</td>
                       <td class="label">样品状态:</td><td class="value">{{sampleStatus}}</td>
                         <td class="label">检测步骤:</td><td class="value">{{testMethod}}</td>
                       
                    </tr>
                    <tr>
                       
                      <td class="label">参考波长:</td><td class="value">{{refWave}}</td>
                      <td class="label">环境温度:</td><td class="value">{{testTemperature}}</td>
                       <td class="label">检测依据:</td><td class="value">{{testBasis}}</td>
                      
                      
                    </tr>
                    <tr>
                      <td class="label">检测时间:</td><td class="value">{{testTime}}</td>
                      <td class="label">相对湿度:</td><td class="value">{{testRelativeHumidity}}</td>
                      <td class="label">检测员:</td><td class="value">{{testOperator}}</td>

                    </tr>
                </table>
                <div class="plate-section">
                    <table class="plate-table">
                        <thead>
                            <tr>
                                <th> </th>
                                <th>1</th><th>2</th><th>3</th><th>4</th><th>5</th><th>6</th><th>7</th><th>8</th><th>9</th><th>10</th><th>11</th><th>12</th>
                            </tr>
                        </thead>
                        <tbody>
                            {{plateData}}
                        </tbody>
                    </table>
                </div>
                 <tr/>
                <div class="note">
                   <p>{{note}} </p>                  
                </div>

                <tr/>
                <div class="divider"></div>
                <tr/>
             
                <div class="sign-area">
                    <span style="font-weight: bold;">审核者：{{testReviewer}}</span>
                    <span style="font-weight: bold;">报告时间：{{generateTime}}</span>
                </div>
            </div>
            </div>
        </body>
        </html>
        `;
    }

    // 获取默认英文模板
    private getDefaultEnTemplate(): string {
        return `
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>{{reportTitle}}</title>
            <style>
                @page {
                    size: A4;
                    margin: 10mm 15mm 10mm 15mm;
                }
                body {
                    font-family: 'MiSans-Normal','MiSans-Regular','Microsoft YaHei', 'SimSun', Arial, sans-serif;
                    margin: 0;
                    padding: 0;
                    background: #f4f8fb;
                }
                .report-bg {
                    min-height: 100vh;
                    display: flex;
                    justify-content: center;
                    align-items: flex-start;
                    padding: 48px 0;
                    background: linear-gradient(90deg, #f4f8fb 60%, #eaf3fa 100%);
                }
                .container {
                    width: 100%;
                    max-width: 980px;
                    margin: 0 32px;
                    background: #fff;
                    border-radius: 16px;
                    box-shadow: 0 6px 32px rgba(25, 118, 210, 0.10), 0 1.5px 6px rgba(25, 118, 210, 0.06);
                    padding: 40px 56px 28px 56px;
                    border: 1.5px solid #e3eaf5;
                }
                .report-title {
                    text-align: center;
                    font-size: 30px;
                    font-weight: bold;
                    color: #1976d2;
                    margin-bottom: 4px;
                    letter-spacing: 2px;
                }
                .report-subtitle {
                    text-align: center;
                    font-size: 18px;
                    font-weight: bold;
                    color: #1565c0;
                    margin-top:18px;
                    margin-bottom: 32px;
                }
                .divider {
                    width: 100%;
                    height: 2px;
                    background: #1976d2;
                    border-radius: 2px;
                    margin: 0 auto 22px auto;
                }
                .info-table {
                    width: 95%;
                    border-collapse: separate;
                    border-spacing: 0 2px;
                    margin: auto auto 15px 5px;
                    font-size: 14px;
                    table-layout: fixed;
                }
                .info-table td {
                    padding: 5px 2px 5px 2px;
                    border: none;
                }
                .info-table .label {
                    color: #1976d2;
                    font-weight: bold;
                    text-align: lefg;
                    width: 125px;
                    white-space: nowrap;
                  
                }
                .info-table .value {
                    color: #222;
                    text-align: left;
                    width: 200px;
                     font-size: 13px;
    
                }
                .plate-section {
                    margin-top: 18px;
                }
                .plate-table {
                    width: 100%;
                    border-collapse: collapse;
                    margin: 0 0 10px 0;
                    font-size: 13px;
                    background: #fafdff;
                    border-radius: 8px;
                    overflow: hidden;
                    table-layout: fixed;
                }
                .plate-table th, .plate-table td {
                    border: 1px solid #c7d7ea;
                    padding: 2px 2px;
                    text-align: center;
                    height: 32px;
                    line-height: 1.4;
                    width: 70px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    font-size: 12px;
                }
                .plate-table th {
                    background: #e3f0fc;
                    font-weight: bold;
                    color: #1976d2;
                }
                .plate-table tr th:first-child,
                .plate-table tr td:first-child {
                    background: #f4f8fb;
                    font-weight: bold;
                    color: #1976d2;
                    width: 45px;
                }
                .note {
                    margin-top: 18px;
                    margin-bottom:18px;
                    font-size: 13px;
                    color: #4a5a6a;
                    background:rgb(255, 255, 255);
                    border-radius: 6px;
                    padding: 8px 16px;
                }
                .note p {
                    margin: 2px 0;
                }
                .sign-area {
                    margin-top: 32px;
                    display: flex;
                    justify-content: flex-end;
                    font-size: 15px;
                    color: #1976d2;
                }
                .sign-area span {
                    margin-left: 100px;
                }
                @media print {
                    body { background: #fff; }
                    .report-bg { background: #fff; padding: 0; }
                    .container { box-shadow:none; border-radius: 0; margin: 0; padding: 12mm 0 8mm 0; }
                }
            </style>
        </head>
        <body>
            <div class="report-bg">
            <div class="container">
                <div class="report-title">{{reportTitle}}</div>
                <div class="report-subtitle">{{reportSubtitle}}</div>
                <div class="divider"></div>
             
                 <table class="info-table">
                    <tr>
                       <td class="label">Plate Number:</td><td class="value">{{mtpNumber}}</td>
                       <td class="label">Positive Formula:</td><td class="value">{{positiveFormula}}</td>
                       <td class="label">Reagent Batch:</td><td class="value">{{reagentBatch}}</td>
                      
                    </tr>
                    <tr>
                       <td class="label">Test Instrument:</td><td class="value">{{testInstrument}}</td>
                       <td class="label">Cutoff Value:</td><td class="value">{{cutoffValue}}</td>
                       <td class="label">Reagent Expiry:</td><td class="value">{{reagentExpiry}}</td>
                      
                      
                    </tr>
                    <tr>
                       <td class="label">Test Project:</td><td class="value">{{projectName}}</td>
                       <td class="label">Sample Source:</td><td class="value">{{sampleSource}}</td>
                       <td class="label">Supplier:</td><td class="value">{{reagentSupplier}}</td>
                       
                       
                    </tr>
                    <tr>
                        
                       <td class="label">Test Wave:</td><td class="value">{{testWave}}</td>
                       <td class="label">Sample Status:</td><td class="value">{{sampleStatus}}</td>
                         <td class="label">Test Method:</td><td class="value">{{testMethod}}</td>
                       
                    </tr>
                    <tr>
                       
                      <td class="label">Ref Wave:</td><td class="value">{{refWave}}</td>
                      <td class="label">Temperature:</td><td class="value">{{testTemperature}}</td>
                       <td class="label">Test Basis:</td><td class="value">{{testBasis}}</td>
                      
                      
                    </tr>
                    <tr>
                      <td class="label">Test Time:</td><td class="value">{{testTime}}</td>
                      <td class="label">RH:</td><td class="value">{{testRelativeHumidity}}</td>
                      <td class="label">Test Operator:</td><td class="value">{{testOperator}}</td>

                    </tr>
                </table>
                <div class="plate-section">
                    <table class="plate-table">
                        <thead>
                            <tr>
                                <th> </th>
                                <th>1</th><th>2</th><th>3</th><th>4</th><th>5</th><th>6</th><th>7</th><th>8</th><th>9</th><th>10</th><th>11</th><th>12</th>
                            </tr>
                        </thead>
                        <tbody>
                            {{plateData}}
                        </tbody>
                    </table>
                </div>
                      <div class="note">
                   <p>{{note}} </p>                  
                </div>
                <tr/>
                <div class="divider"></div>
                <tr/>
             
                <div class="sign-area">
                    <span style="font-weight: bold;">Reviewer:</span><span style="margin-left: 10px;">{{testReviewer}}</span>
                    <span style="font-weight: bold;">Report Time:</span><span style="margin-left: 10px;">{{generateTime}}</span>
                </div>
            </div>
            </div>  
        </body>
        </html>
        `;
    }

    // 根据项目代码和语言选择模板
    public selectTemplate(projectCode: string, language: 'zh' | 'en'): Template {
        // 首先尝试查找项目特定的模板
        const projectTemplateKey = `${projectCode}-${language}`;
        const projectTemplate = this.templates.get(projectTemplateKey);

        if (projectTemplate) {
            return projectTemplate;
        }

        // 如果没有项目特定模板，返回默认模板
        const defaultTemplateKey = `default-${language}`;
        const defaultTemplate = this.templates.get(defaultTemplateKey);

        if (!defaultTemplate) {
            throw new Error(
                `No template found for project ${projectCode} and language ${language}`
            );
        }

        return defaultTemplate;
    }

    // 渲染模板
    public renderTemplate(template: Template, data: Record<string, unknown>): string {
        let html = template.html;

        // 简单的模板变量替换
        Object.keys(data).forEach((key) => {
            const placeholder = `{{${key}}}`;
            const value = data[key] !== undefined && data[key] !== null ? data[key] : '';
            html = html.replace(new RegExp(placeholder, 'g'), String(value));
        });

        // 处理条件块
        html = this.processConditionalBlocks(html, data);

        return html;
    }

    // 处理条件块
    private processConditionalBlocks(html: string, data: Record<string, unknown>): string {
        // 简单的条件块处理 {{#if condition}}...{{/if}}
        const ifRegex = /\{\{#if\s+(\w+)\}\}([\s\S]*?)\{\{\/if\}\}/g;

        return html.replace(ifRegex, (_, condition, content) => {
            if (data[condition]) {
                return content;
            }
            return '';
        });
    }

    // 生成96孔板数据HTML
    public generatePlateDataHtml(record: TestRecord): string {
        const rows = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H'];
        const cols = 12;

        return rows
            .map((row) => {
                const cells = Array(cols)
                    .fill('')
                    .map((_, colIndex) => {
                        const wellId = `${row}${colIndex + 1}`;
                        const wellData = record.wellData?.[wellId];

                        const cellLines = [
                            wellData?.sampleType.type === 'sample'
                                ? wellData?.sampleNumber
                                : wellData?.sampleType.name,
                            wellData?.odValue !== undefined ? wellData.odValue.toFixed(4) : '',
                            // wellData?.concentration !== undefined
                            //     ? wellData.concentration.toFixed(2)
                            //     : '',
                            wellData?.result !== undefined
                                ? wellData?.result === 0
                                    ? '-'
                                    : wellData?.result === 1
                                      ? '+'
                                      : '-'
                                : ''
                        ];

                        return `<td>${cellLines.join('<br/>')}</td>`;
                    });

                return `<tr><th>${row}</th>${cells.join('')}</tr>`;
            })
            .join('');
    }

    // 准备模板数据
    public prepareTemplateData(request: PrintRequest): Record<string, unknown> {
        const { testRecord, options } = request;
        const lang = options.language || 'zh';
        // 类型安全地访问 reportTitle 和 reportSubtitle
        const { reportTitle, reportSubtitle } = options as Partial<{
            reportTitle: string;
            reportSubtitle: string;
        }>;

        // 从 TestRecord 和 TestAdditionalInfo 中获取所有数据
        const additionalInfo = testRecord.testAdditionalInfo || {};

        return {
            // 报告标题相关
            reportTitle:
                reportTitle ||
                additionalInfo.mainTitle ||
                (lang === 'en' ? 'Elisa Report' : 'Elisa报告'),
            reportSubtitle:
                reportSubtitle ||
                additionalInfo.subTitle ||
                (lang === 'en' ? 'OD Value and Result Report' : 'OD值及结果报告'),

            // 基本检测信息
            testTime: testRecord.testDate.toLocaleString(),
            mtpNumber: testRecord.mtpNumber,
            projectName: testRecord.testProject.name,
            projectCode: testRecord.testProject.code,
            cutoffValue: testRecord.cutOffValue.toFixed(4),

            // 项目参数
            testWave: testRecord.testProject.testWave || '',
            refWave: testRecord.testProject.refWave || '',
            useBlankCorrection: testRecord.testProject.useBlankCorrection ? '是' : '否',
            positiveFormula: testRecord.testProject.cutOffFormula || '',
            positiveJudge: testRecord.testProject.postiveJudge || '',

            // 试剂信息
            reagentBatch: additionalInfo.reagentBatch || '',
            reagentExpiry: additionalInfo.reagentExpiry || '',
            reagentSupplier: additionalInfo.reagentSupplier || '',

            // 检测环境
            testTemperature:
                additionalInfo.testTemperature !== undefined
                    ? `${additionalInfo.testTemperature}℃`
                    : '',
            testRelativeHumidity:
                additionalInfo.testRelativeHumidity !== undefined
                    ? `${additionalInfo.testRelativeHumidity}%`
                    : '',

            // 检测人员
            testOperator: additionalInfo.testOperator || '',
            testReviewer: additionalInfo.testReviewer || '',

            // 检测方法和依据
            testMethod: additionalInfo.testMethod || '试剂盒',
            testType: additionalInfo.testType || 'ELISA',
            testBasis: additionalInfo.testBasis || 'ELISA试剂盒说明书',
            testInstrument: additionalInfo.testInstrument || '',

            // 样本信息
            sampleSource: additionalInfo.sampleSource || '',
            sampleStatus: additionalInfo.sampleStatus || '正常',
            sampleType: additionalInfo.sampleType || '',
            sampleNumber: additionalInfo.sampleNumber || '',

            // 备注信息
            note: additionalInfo.note || '注：“+” = 阳性，“-” = 阴性',

            // 板数据
            includePlateData: options.includePlateData,
            plateData: options.includePlateData ? this.generatePlateDataHtml(testRecord) : '',

            // 生成时间
            generateTime: new Date().toLocaleString()
        };
    }

    // 渲染完整的HTML
    public render(request: PrintRequest): string {
        const template = this.selectTemplate(
            request.testRecord.testProject.code,
            request.options.language
        );
        const data = this.prepareTemplateData(request);
        return this.renderTemplate(template, data);
    }
}
