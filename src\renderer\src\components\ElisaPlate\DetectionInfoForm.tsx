import React, { useEffect, useState } from 'react';
import {
    FormControl,
    FormLabel,
    Select,
    Input,
    InputGroup,
    InputLeftAddon,
    InputRightElement,
    HStack
} from '@chakra-ui/react';
import { Project, TestAdditionalInfo } from '@shared/types';
import { generateProjectTimestamp } from '@renderer/utils';
import { useDeviceFilters } from '@renderer/hooks/useDeviceFilters';
import logger from '@renderer/utils/logger';

// 试剂供应商存储key
const REAGENT_SUPPLIER_STORE_KEY = 'app.reagentSupplier';

// 定义标签宽度常量
const LABEL_WIDTH = '80px';

interface DetectionInfoFormProps {
    id?: string;
    selectedProject: Project;
    testAdditionalInfo: TestAdditionalInfo;
    setTestAdditionalInfo: (info: TestAdditionalInfo) => void;
}

const DetectionInfoForm: React.FC<DetectionInfoFormProps> = ({
    id,
    selectedProject,
    testAdditionalInfo,
    setTestAdditionalInfo
}) => {
    // const [suppliers] = useState<string[]>([]);
    const [lastProjectId, setLastProjectId] = useState<string | null>(null);
    const [suppliers, setSuppliers] = useState<string[]>([]);

    // 使用通用的设备滤光片Hook
    const { defaultDevice } = useDeviceFilters(true);

    useEffect(() => {
        // 当选中的项目改变时，更新微板编号
        if (selectedProject?.id !== lastProjectId) {
            const number = generateProjectTimestamp(selectedProject.code);
            console.log('项目变更，更新微板编号:', number);

            setTestAdditionalInfo({
                ...testAdditionalInfo,
                mtpNumber: number,
                testInstrument: defaultDevice
            });
            setLastProjectId(selectedProject.id);
        }
        loadReagentSupplier();
    }, [selectedProject, lastProjectId, testAdditionalInfo, setTestAdditionalInfo]);

    const handleInputChange = (field: keyof TestAdditionalInfo, value: string | number) => {
        setTestAdditionalInfo({
            ...testAdditionalInfo,
            [field]: value
        });

        console.log('testAdditionalInfo', testAdditionalInfo);
    };

    // 从配置文件加载供应商列表
    const loadReagentSupplier = async () => {
        try {
            const response = await window.customApi.store.get<string[]>(REAGENT_SUPPLIER_STORE_KEY);
            if (response.success && response.data) {
                setSuppliers(response.data);
            }
        } catch (error) {
            logger.error('Failed to load reagent suppliers:', error, {
                component: './src/renderer/src/components/report/ReagentSupplier.tsx'
            });
        }
    };

    return (
        <FormControl
            id={id}
            as="fieldset"
            borderWidth="1px"
            borderRadius="md"
            p={{ base: 1, md: 2, lg: 3 }}
            pt={0}
            m={0}
            h="100%"
            display="flex"
            flexDirection="column"
            justifyContent={'space-between'}
        >
            <FormLabel as="legend">检测信息</FormLabel>
            <InputGroup alignItems="center">
                <InputLeftAddon width={LABEL_WIDTH} bg="transparent" border="none" pl={0}>
                    微板编号
                </InputLeftAddon>
                <Input
                    size="sm"
                    value={testAdditionalInfo.mtpNumber || ''}
                    onChange={(e) => handleInputChange('mtpNumber', e.target.value)}
                    placeholder="请输入微板编号"
                    tabIndex={1}
                />
            </InputGroup>

            <InputGroup alignItems="center">
                <InputLeftAddon width={LABEL_WIDTH} bg="transparent" border="none" pl={0}>
                    试剂厂商
                </InputLeftAddon>
                <Select
                    size="sm"
                    value={testAdditionalInfo.reagentSupplier || ''}
                    onChange={(e) => handleInputChange('reagentSupplier', e.target.value)}
                    placeholder="请选择试剂厂商"
                >
                    {suppliers.map((supplier) => (
                        <option key={supplier} value={supplier}>
                            {supplier}
                        </option>
                    ))}
                </Select>
            </InputGroup>

            <HStack>
                <InputGroup alignItems="center">
                    <InputLeftAddon width={LABEL_WIDTH} bg="transparent" border="none" pl={0}>
                        试剂批号
                    </InputLeftAddon>
                    <Input
                        size="sm"
                        value={testAdditionalInfo.reagentBatch || ''}
                        onChange={(e) => handleInputChange('reagentBatch', e.target.value)}
                        placeholder="请输入试剂批号"
                        tabIndex={2}
                    />
                </InputGroup>

                <InputGroup alignItems="center">
                    <InputLeftAddon width={LABEL_WIDTH} bg="transparent" border="none" pl={0}>
                        试剂效期
                    </InputLeftAddon>
                    <Input
                        size="sm"
                        type="date"
                        value={testAdditionalInfo.reagentExpiry || ''}
                        onChange={(e) => handleInputChange('reagentExpiry', e.target.value)}
                        placeholder="请选择试剂效期"
                        tabIndex={3}
                    />
                </InputGroup>
            </HStack>

            <HStack>
                <InputGroup alignItems="center">
                    <InputLeftAddon width={LABEL_WIDTH} bg="transparent" border="none" pl={0}>
                        温度
                    </InputLeftAddon>
                    <Input
                        size="sm"
                        type="number"
                        value={testAdditionalInfo.testTemperature || ''}
                        onChange={(e) =>
                            handleInputChange('testTemperature', parseFloat(e.target.value))
                        }
                        placeholder="请输入检测温度"
                    />
                    <InputRightElement>℃</InputRightElement>
                </InputGroup>

                <InputGroup alignItems="center">
                    <InputLeftAddon width={LABEL_WIDTH} bg="transparent" border="none" pl={0}>
                        相对湿度
                    </InputLeftAddon>
                    <Input
                        size="sm"
                        type="number"
                        value={testAdditionalInfo.testRelativeHumidity || ''}
                        onChange={(e) =>
                            handleInputChange('testRelativeHumidity', parseFloat(e.target.value))
                        }
                        placeholder="请输入相对湿度"
                        tabIndex={4}
                    />
                    <InputRightElement>%</InputRightElement>
                </InputGroup>
            </HStack>
            <HStack>
                <InputGroup alignItems="center">
                    <InputLeftAddon width={LABEL_WIDTH} bg="transparent" border="none" pl={0}>
                        样品来源
                    </InputLeftAddon>
                    <Input
                        size="sm"
                        value={testAdditionalInfo.sampleSource || ''}
                        onChange={(e) => handleInputChange('sampleSource', e.target.value)}
                        placeholder="请输入样品来源"
                        tabIndex={5}
                    />
                </InputGroup>
                <InputGroup alignItems="center">
                    <InputLeftAddon width={LABEL_WIDTH} bg="transparent" border="none" pl={0}>
                        样品状态
                    </InputLeftAddon>
                    <Input
                        size="sm"
                        value={testAdditionalInfo.sampleStatus || ''}
                        onChange={(e) => handleInputChange('sampleStatus', e.target.value)}
                        placeholder="请输入样品状态"
                        tabIndex={5}
                    />
                </InputGroup>
            </HStack>
        </FormControl>
    );
};

export default DetectionInfoForm;
