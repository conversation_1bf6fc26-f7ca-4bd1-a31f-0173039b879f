// 拟合模型类型
export type FittingModel =
    | 'LINEAR'
    | 'POLYNOMIAL'
    | 'LOG_LOG'
    | 'LOGISTIC_3P'
    | 'LOGISTIC_4P'
    | 'LOGISTIC_5P'
    | 'CUBIC_SPLINE';

// 标准曲线数据
export interface StandardCurveData {
    concentrations: number[];
    responses: number[];
    name?: string;
}

// 拟合选项
export interface FittingOptions {
    model: FittingModel;
    maxIterations?: number;
    errorTolerance?: number;
    damping?: number;
}

// 质量指标
export interface QualityMetrics {
    isValid: boolean;
    warnings: string[];
    rSquared: number;
    maxResidual: number;
    convergence: boolean;
}

// 拟合结果
export interface FittingResult {
    model: FittingModel;
    parameters: number[];
    rSquared: number;
    residuals: number[];
    predict: (x: number) => number;
    predictConcentration: (y: number) => number;
    equation: string;
    convergence: boolean;
    iterations: number;
    qualityMetrics: QualityMetrics | null;
}

export interface FittingOptions {
    model: FittingModel;
    maxIterations?: number;
    errorTolerance?: number;
    damping?: number;
    polynomialDegree?: number; // 多项式拟合的阶数
}
