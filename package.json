{"name": "khb_st_elisa", "companyName": "Kehua (Xi'an) Bio-engineering Co., Ltd.", "author": "<PERSON><PERSON><PERSON><PERSON>", "version": "1.0.1-beta.2", "private": true, "license": "MIT", "description": "KHB ELISA ST SERIAL INSTRUMEN CONTRON SYSTEM SOFTWARE", "homepage": "https://www.skhb.com/", "main": "./out/main/main.js", "scripts": {"format": "prettier --write .", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "typecheck:node": "tsc --noEmit -p tsconfig.node.json --composite false", "typecheck:web": "tsc --noEmit -p tsconfig.web.json --composite false", "typecheck": "npm run typecheck:node && npm run typecheck:web", "clean": "<PERSON><PERSON><PERSON> dist out", "check": "node scripts/check-file-usage.js", "build": "npm run typecheck && electron-vite build", "rebuild": "npm run clean && npm run build", "preview": "electron-vite preview", "dev": "electron-vite dev", "start": "electron .", "build version---------------------------------------------------------": "", "build:win-unpacked": "npm run build && electron-builder --dir --win --config", "build:win": "npm run build && electron-builder --win --config", "build:dev": "npm run version:dev && npm run build:win-unpacked", "build:beta": "npm run version:beta && npm run build:win", "build:alpha": "npm run version:alpha && npm run build:win", "build:stable": "npm run version:stable&& npm run build:win", "version modify---------------------------------------------------------------": "", "version:dev": "npm version prerelease --preid=dev --no-git-tag-version", "version:beta": "npm version prerelease --preid=beta --no-git-tag-version", "version:alpha": "npm version prerelease --preid=alpha --no-git-tag-version", "version:stable": "npm version patch --no-git-tag-version", "prisma sqlite---------------------------------------------------------": "", "prisma:generate": "prisma generate", "prisma:init": "prisma init", "prisma:createDb": "prisma migrate dev --name init", "prisma:migrate-add_new_field": "prisma migrate dev --name add_new_field", "prisma:sync": "prisma migrate dev && prisma generate", "chcp65001": "chcp 65001", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@chakra-ui/icons": "^2.2.4", "@chakra-ui/react": "^2.10.7", "@codemirror/lang-javascript": "^6.2.4", "@codemirror/language": "^6.11.2", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@prisma/client": "^5.22.0", "@uiw/react-codemirror": "^4.23.14", "dayjs": "^1.11.13", "electron-store": "^8.0.1", "electron-updater": "^6.3.9", "exceljs": "^4.4.0", "framer-motion": "^12.9.1", "i18next": "^25.2.0", "i18next-http-backend": "^3.0.2", "mathjs": "^14.5.2", "ml-levenberg-marquardt": "^5.0.0", "ml-regression": "^6.3.0", "pdfmake": "^0.2.20", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "^15.5.1", "react-router-dom": "^7.5.2", "recharts": "^3.1.0", "serialport": "^12.0.0", "spline-interpolator": "^1.0.0", "winston": "^3.17.0"}, "devDependencies": {"@electron-toolkit/eslint-config-prettier": "^3.0.0", "@electron-toolkit/eslint-config-ts": "^3.0.0", "@electron-toolkit/preload": "^3.0.1", "@electron-toolkit/tsconfig": "^1.0.1", "@electron-toolkit/utils": "^4.0.0", "@eslint/js": "^9.25.1", "@serialport/bindings-cpp": "^12.0.0", "@types/exceljs": "^0.5.3", "@types/jest": "^30.0.0", "@types/node": "^22.15.1", "@types/pdfmake": "^0.2.11", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@vitejs/plugin-react": "^4.3.4", "electron": "^28.0.0", "electron-builder": "^26.0.12", "electron-vite": "^3.1.0", "eslint": "^9.25.1", "eslint-config-prettier": "^10.1.2", "eslint-plugin-prettier": "^5.2.6", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "jest": "^30.0.5", "prettier": "3.5.3", "prisma": "^5.22.0", "rimraf": "^5.0.0", "ts-jest": "^29.4.0", "typescript": "^5.8.3", "typescript-eslint": "^8.31.0", "vite": "^5.0.0"}}