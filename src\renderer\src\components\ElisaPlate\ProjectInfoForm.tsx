import React, { useEffect, useState } from 'react';
import { MTPLayoutType } from '@shared/commondefines';

import {
    FormControl,
    FormLabel,
    RadioGroup,
    Radio,
    SimpleGrid,
    Checkbox,
    Select,
    InputGroup,
    InputLeftAddon,
    Input,
    InputRightElement,
    Flex,
    Box,
    Text
} from '@chakra-ui/react';

import { Project } from '@shared/types';
import { useToast } from '@chakra-ui/react';
import FormulaInputField from '../FormulaInputField';
import { useDeviceFilters } from '@renderer/hooks/useDeviceFilters';
import logger from '../../utils/logger';

interface ProjectInfoFormProps {
    id?: string;
    layoutType: MTPLayoutType;
    onLayoutTypeChange: (layoutType: MTPLayoutType) => void;
    useCommonBlank: boolean;
    onUseCommonBlankChange: (useCommonBlank: boolean) => void;
    refreshKey?: number;
    selectedProject: Project;
    onSelectedProjectChange: (project: Project) => void;
    onSingleProjectChange?: (project: Project) => void; // 新增：单项目变化回调
}

export const ProjectInfoForm: React.FC<ProjectInfoFormProps> = ({
    id,
    layoutType,
    onLayoutTypeChange,
    useCommonBlank,
    onUseCommonBlankChange,
    refreshKey = 0,
    selectedProject,
    onSelectedProjectChange,
    onSingleProjectChange
}) => {
    const [projects, setProjects] = useState<Project[]>([]);
    const toast = useToast();
    // 使用通用的设备滤光片Hook
    const { filters } = useDeviceFilters(true);

    useEffect(() => {
        // 从接口获取数据
        const fetchProjects = async () => {
            try {
                const result = await window.customApi.configInfo.getProjectList();
                if (result.success && result.data) {
                    setProjects(result.data);
                    // 默认不选中任何项目（空白选项）
                    const emptyProject = {} as Project;
                    onSelectedProjectChange(emptyProject);
                    // 如果是单项目布局，同时清空单项目分配
                    if (layoutType === MTPLayoutType.SingleProjectLayout && onSingleProjectChange) {
                        onSingleProjectChange(emptyProject);
                    }
                } else {
                    logger.error('Failed to load project list:', result.error, {
                        component: './src/renderer/src/components/ElisaPlate/ProjectInfoForm.tsx'
                    });

                    toast({
                        title: '加载项目列表失败',
                        description: result.error || '数据库操作失败',
                        status: 'error',
                        duration: 3000,
                        position: 'top',
                        isClosable: true
                    });
                }
            } catch (error) {
                logger.error('Failed to load project list:', error, {
                    component: './src/renderer/src/components/ElisaPlate/ProjectInfoForm.tsx'
                });

                toast({
                    title: '加载项目列表失败',
                    description: '系统错误，请重试',
                    status: 'error',
                    duration: 3000,
                    position: 'top',
                    isClosable: true
                });
            }
        };
        fetchProjects();
    }, [refreshKey]);

    const handleProjectChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
        if (event.target.value === '') {
            // 选择空白选项
            const emptyProject = {} as Project;
            onSelectedProjectChange(emptyProject);
            // 如果是单项目布局，同时清空单项目分配
            if (layoutType === MTPLayoutType.SingleProjectLayout && onSingleProjectChange) {
                onSingleProjectChange(emptyProject);
            }
            console.log('清空项目选择');
        } else {
            const foundProject = projects.find((project) => project.id === event.target.value);
            if (foundProject) {
                onSelectedProjectChange(foundProject);
                // 如果是单项目布局，同时更新单项目分配
                if (layoutType === MTPLayoutType.SingleProjectLayout && onSingleProjectChange) {
                    onSingleProjectChange(foundProject);
                }
                console.log('选择检测项目：', foundProject);
            }
        }
    };

    const handleLayoutTypeChange = (newLayoutType: MTPLayoutType) => {
        onLayoutTypeChange(newLayoutType);

        // 当切换到单项目布局时，自动将当前选中的项目设置为单项目分配
        if (
            newLayoutType === MTPLayoutType.SingleProjectLayout &&
            selectedProject.id &&
            onSingleProjectChange
        ) {
            onSingleProjectChange(selectedProject);
        } else if (
            newLayoutType === MTPLayoutType.SingleProjectLayout &&
            !selectedProject.id &&
            onSingleProjectChange
        ) {
            // 如果没有选中项目，清空单项目分配
            onSingleProjectChange({} as Project);
        }
    };

    return (
        <FormControl
            id={id}
            as="fieldset"
            borderWidth="1px"
            borderRadius="md"
            p={{ base: 1, md: 2, lg: 3 }}
            // p={2}
            m={0}
            h="100%"
            display="flex"
            flexDirection="column"
            gap={2}
            // alignItems="center"
            // justifyContent="center"
        >
            <FormLabel as="legend" fontSize="md">
                项目信息
            </FormLabel>

            <RadioGroup value={layoutType} onChange={handleLayoutTypeChange}>
                <SimpleGrid mt={2} columns={2} spacing={2}>
                    <Radio value={MTPLayoutType.SingleProjectLayout}>
                        {MTPLayoutType.SingleProjectLayout}
                    </Radio>
                    <Radio value={MTPLayoutType.MultiProjectHorizontalLayout}>
                        {MTPLayoutType.MultiProjectHorizontalLayout}
                    </Radio>
                    <Radio value={MTPLayoutType.MultiProjectVerticalLayout}>
                        {MTPLayoutType.MultiProjectVerticalLayout}
                    </Radio>
                    <Checkbox
                        isChecked={useCommonBlank}
                        onChange={(e) => onUseCommonBlankChange(e.target.checked)}
                    >
                        共用空白
                    </Checkbox>
                </SimpleGrid>
            </RadioGroup>

            <SimpleGrid columns={2} spacing={1} flex={1}>
                <InputGroup alignItems="center" gridColumn={'span 2'}>
                    <InputLeftAddon bg="transparent" border="none" pl={0}>
                        项目名称
                    </InputLeftAddon>
                    <Select
                        size="sm"
                        value={selectedProject.id || ''}
                        onChange={handleProjectChange}
                    >
                        <option value="">请选择项目</option>
                        {projects.map((project) => (
                            <option key={project.id} value={project.id}>
                                {project.name}
                            </option>
                        ))}
                    </Select>
                </InputGroup>

                <InputGroup alignItems="center" gridColumn={'span 2'}>
                    <InputLeftAddon bg="transparent" border="none" pl={0}>
                        项目代码
                    </InputLeftAddon>
                    <Input size="sm" isReadOnly value={selectedProject.code || ''} />
                </InputGroup>

                <InputGroup alignItems="center">
                    <InputLeftAddon bg="transparent" border="none" pl={0}>
                        测试波长
                    </InputLeftAddon>
                    <Input
                        size="sm"
                        isReadOnly
                        value={
                            filters.find((f) => f.no === selectedProject.testWave)?.wavelength || ''
                        }
                    />
                    <InputRightElement textColor={'gray.400'}>(nm) </InputRightElement>
                </InputGroup>

                <InputGroup alignItems="center">
                    <InputLeftAddon bg="transparent" border="none" pl={0}>
                        参考波长
                    </InputLeftAddon>
                    <Input
                        size="sm"
                        isReadOnly
                        value={
                            filters.find((f) => f.no === selectedProject.refWave)?.wavelength || ''
                        }
                    />
                    <InputRightElement textColor={'gray.400'}>(nm) </InputRightElement>
                </InputGroup>

                {/* <InputGroup alignItems="center" gridColumn={'span 2'}>
                    <InputLeftAddon bg="transparent" border="none" pl={0}>
                        振板时间
                    </InputLeftAddon>
                    <Input size="sm" isReadOnly value={selectedProject.shakeTime || ''} />
                    <InputRightElement textColor={'gray.400'}>(Sec.) </InputRightElement>
                </InputGroup> */}
                <Flex align="center" gridColumn={'span 2'}>
                    阳性判断
                    <Text ml={4} textAlign={'center'} px={3} border={'1px solid red'}>
                        {selectedProject.postiveJudge || ''}
                    </Text>
                    <Box flex="1" ml={1}>
                        <FormulaInputField
                            value={selectedProject.cutOffFormula || ''}
                            onChange={() => {}}
                            singleLine={true}
                            showEditButton={false}
                        />
                    </Box>
                </Flex>
            </SimpleGrid>
        </FormControl>
    );
};
export default ProjectInfoForm;
