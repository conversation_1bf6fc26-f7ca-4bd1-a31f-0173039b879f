import React, { useState, useEffect } from 'react';
import {
    Table,
    Thead,
    Tbody,
    Tr,
    Th,
    Td,
    TableContainer,
    Box,
    Text,
    Button,
    VStack,
    HStack
} from '@chakra-ui/react';
import {
    processTableDataForMerge,
    createSampleDataWithTime,
    formatTimeForDisplay
} from '@renderer/utils';

const MergedTableExample: React.FC = () => {
    const [processedData, setProcessedData] = useState<
        Array<{
            rowData: string[];
            mergeInfo: Array<{ rowSpan: number; shouldShow: boolean }>;
        }>
    >([]);

    useEffect(() => {
        // 创建示例数据
        const sampleData = createSampleDataWithTime(12);

        // 处理数据用于合并（合并第一列，即时间列）
        const processed = processTableDataForMerge(sampleData, [0]);
        setProcessedData(processed);
    }, []);

    const refreshData = () => {
        const sampleData = createSampleDataWithTime(12);
        const processed = processTableDataForMerge(sampleData, [0]);
        setProcessedData(processed);
    };

    return (
        <VStack spacing={4} align="stretch" w="100%">
            <HStack justify="space-between">
                <Text fontSize="lg" fontWeight="bold">
                    表格第一列时间合并示例
                </Text>
                <Button size="sm" onClick={refreshData}>
                    刷新数据
                </Button>
            </HStack>

            <Box border="1px" borderColor="gray.200" borderRadius="md" overflow="hidden">
                <TableContainer>
                    <Table variant="simple" size="sm">
                        <Thead>
                            <Tr bg="gray.50">
                                <Th textAlign="center" fontSize="sm">
                                    检测时间
                                </Th>
                                <Th textAlign="center" fontSize="sm">
                                    样本名称
                                </Th>
                                <Th textAlign="center" fontSize="sm">
                                    项目名称
                                </Th>
                                <Th textAlign="center" fontSize="sm">
                                    OD值
                                </Th>
                                <Th textAlign="center" fontSize="sm">
                                    检测结果
                                </Th>
                            </Tr>
                        </Thead>
                        <Tbody>
                            {processedData.map((row, rowIndex) => (
                                <Tr key={rowIndex} _hover={{ bg: 'gray.50' }}>
                                    {row.rowData.map((cell, colIndex) => {
                                        // 对于第一列（时间列），需要处理合并
                                        if (colIndex === 0) {
                                            const mergeInfo = row.mergeInfo[0];

                                            // 如果不需要显示这个单元格（被合并了），返回null
                                            if (!mergeInfo.shouldShow) {
                                                return null;
                                            }

                                            // 如果需要显示，设置rowSpan
                                            return (
                                                <Td
                                                    key={colIndex}
                                                    rowSpan={mergeInfo.rowSpan}
                                                    textAlign="center"
                                                    fontSize="sm"
                                                    fontWeight="bold"
                                                    bg="blue.50"
                                                    borderRight="2px solid"
                                                    borderColor="blue.200"
                                                >
                                                    {formatTimeForDisplay(cell)}
                                                </Td>
                                            );
                                        }

                                        // 其他列正常显示
                                        return (
                                            <Td key={colIndex} textAlign="center" fontSize="sm">
                                                {cell}
                                            </Td>
                                        );
                                    })}
                                </Tr>
                            ))}
                        </Tbody>
                    </Table>
                </TableContainer>
            </Box>

            <Box p={4} bg="gray.50" borderRadius="md">
                <Text fontSize="sm" fontWeight="bold" mb={2}>
                    说明：
                </Text>
                <Text fontSize="xs" color="gray.600">
                    1. 第一列（检测时间）中相同的时间会自动合并单元格
                    <br />
                    2. 合并的单元格会显示为蓝色背景，便于识别
                    <br />
                    3. 每3行数据使用相同的时间，展示合并效果
                    <br />
                    4. 点击&ldquo;刷新数据&rdquo;按钮可以生成新的示例数据
                </Text>
            </Box>
        </VStack>
    );
};

export default MergedTableExample;
