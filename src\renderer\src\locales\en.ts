const enUS = {
    // Common translations
    common: {
        company: "Kehua (Xi'an) Bio-engineering Co., Ltd.",
        appName: 'KHB ELISA ST Series Instrument Control Software',
        about: 'About...',

        mainWindow: {
            tabs: {
                home: 'Home',
                elisaControl: 'ELISA Control',
                resultData: 'Result Data'
            }
        },
        commonDefines: {
            qualitative: 'Qualitative',
            quantitative: 'Quantitative',
            continuous: 'Continuous',
            step: 'Step',
            sampleType: {
                normal: 'Normal Sample',
                blank: 'Blank',
                negativeControl: 'Negative Control',
                weakPositive: 'Weak Positive',
                positiveControl: 'Positive Control',
                standard: 'Standard',
                qualityControl: 'Quality Control'
            },
            deviceNames: {
                unknown: 'Unknown Device',
                st360: 'ST-360 ELISA Reader',
                st960: 'ST-960 ELISA Reader'
            },
            deviceTypes: {
                unknown: 'Unknown Device',
                reader: 'ELISA Reader'
            }
        },
        button: {
            entry: 'Enter...',
            save: 'Save',
            exit: 'Exit',
            cancel: 'Cancel',
            confirm: 'Confirm',
            delete: 'Delete',
            edit: 'Edit',
            add: 'Add',
            start: 'Start',
            stop: 'Stop',
            reset: 'Reset',
            next: 'Next',
            previous: 'Previous',
            finish: 'Finish',
            close: 'Close',
            select: 'Select'
        },
        label: {
            version: 'Version:'
        },
        message: {
            saveSuccess: 'Successfully saved',
            deleteSuccess: 'Successfully deleted',
            updateSuccess: 'Successfully updated',
            createSuccess: 'Successfully created',
            uploadSuccess: 'Successfully uploaded',
            downloadSuccess: 'Successfully downloaded',
            importSuccess: 'Successfully imported',
            exportSuccess: 'Successfully exported',

            saveFailed: 'Save failed',
            deleteFailed: 'Delete failed',
            updateFailed: 'Update failed',
            createFailed: 'Create failed',
            uploadFailed: 'Upload failed',
            downloadFailed: 'Download failed',
            importFailed: 'Import failed',
            exportFailed: 'Export failed',

            confirm: {
                delete: 'Are you sure you want to delete?',
                cancel: 'Are you sure you want to cancel?',
                discard: 'Are you sure you want to discard changes?'
            },
            loading: {
                saving: 'Saving...',
                loading: 'Loading...',
                processing: 'Processing...',
                uploading: 'Uploading...',
                downloading: 'Downloading...'
            }
        },
        form: {
            required: 'This field is required',
            invalid_format: 'Invalid format',
            min_length: 'Minimum length is {{length}} characters',
            max_length: 'Maximum length is {{length}} characters',
            min_value: 'Minimum value is {{value}}',
            max_value: 'Maximum value is {{value}}',
            invalid_email: 'Invalid email address',
            invalid_date: 'Invalid date format',
            invalid_time: 'Invalid time format',
            invalid_number: 'Invalid number format',
            passwords_not_match: 'Passwords do not match',
            select_option: 'Please select an option',
            file_required: 'Please select a file',
            file_type_error: 'Invalid file type',
            file_size_error: 'File size exceeds limit'
        }
    },

    // Component translations
    components: {
        languageSwitch: {
            toast: {
                title: 'Change language',
                success: {
                    description: 'language switch success.'
                },
                error: {
                    description: 'language switch failed, please try again later.'
                }
            }
        },
        detectionInfoForm: {
            title: 'Detection Information', // 检测信息
            fields: {
                mtpNumber: {
                    label: 'Microplate Number', // 微板编号
                    placeholder: 'Please enter microplate number' // 请输入微板编号
                },
                reagentSupplier: {
                    label: 'Reagent Supplier', // 试剂厂商
                    placeholder: 'Please select reagent supplier' // 请选择试剂厂商
                },
                reagentBatch: {
                    label: 'Reagent Batch', // 试剂批号
                    placeholder: 'Please enter reagent batch number' // 请输入试剂批号
                },
                reagentExpiry: {
                    label: 'Reagent Expiry', // 试剂效期
                    placeholder: 'Please select reagent expiry date' // 请选择试剂效期
                },
                testTemperature: {
                    label: 'Temperature', // 温度
                    placeholder: 'Please enter test temperature', // 请输入检测温度
                    unit: '°C' // 摄氏度
                },
                testRelativeHumidity: {
                    label: 'Relative Humidity', // 相对湿度
                    placeholder: 'Please enter relative humidity', // 请输入相对湿度
                    unit: '%' // 百分比
                },
                sampleSource: {
                    label: 'Sample Source', // 样品来源
                    placeholder: 'Please enter sample source' // 请输入样品来源
                },
                sampleStatus: {
                    label: 'Sample Status', // 样品状态
                    placeholder: 'Please enter sample status' // 请输入样品状态
                }
            },
            messages: {
                projectChanged: 'Project changed, updating microplate number:', // 项目变更，更新微板编号:
                testAdditionalInfo: 'Test additional info' // 检测附加信息
            }
        },
        autoNumberPanel: {
            title: 'Sample Numbering Settings', // 样本编号设置
            fields: {
                startWell: {
                    label: 'Start Well', // 起始孔位
                    placeholder: 'Select start well' // 选择起始孔位
                },
                startNumber: {
                    label: 'Start Number', // 起始编号
                    placeholder: 'Enter start number' // 请输入起始编号
                },
                sampleCount: {
                    label: 'Sample Count', // 样本数量
                    placeholder: 'Enter sample count' // 请输入样本数量
                },
                direction: {
                    label: 'Numbering Direction', // 编号方向
                    vertical: 'Vertical Numbering', // 纵向编号
                    horizontal: 'Horizontal Numbering' // 横向编号
                },
                reverse: {
                    label: 'Reverse Numbering' // 逆序编号
                }
            },
            buttons: {
                autoNumber: 'Auto Number', // 自动编号
                clearNumbering: 'Clear Numbering' // 清除编号
            },
            messages: {
                selectStartWell: 'Please select start well first', // 请先选择起始孔位
                selectedWells: 'Selected wells', // 已选择孔位
                noSelectedWells: 'No wells selected' // 未选择孔位
            }
        },
        elisaPlateForm: {
            title: 'ELISA Plate Layout', // 酶标板布局
            messages: {
                selectProject: 'Please select project', // 请选择项目
                multiProject: 'Multi Project', // 多项目
                clickToSetProject: 'Click to set project', // 点击设置项目
                wellInfo: 'Well Information', // 孔位信息
                sampleType: 'Sample Type', // 样本类型
                sampleNumber: 'Sample Number', // 样本编号
                mainWavelengthOD: 'Main Wavelength OD', // 主波长OD值
                refWavelengthOD: 'Reference Wavelength OD', // 参考波长OD值
                wellPosition: 'Well Position' // 孔位
            }
        },
        sampleTypeForm: {
            title: 'Sample Type',

            validation: {
                type_required: 'Sample type is required',
                volume_required: 'Sample volume is required',
                volume_range: 'Volume must be between {{min}} and {{max}} μL',
                dilution_required: 'Dilution ratio is required',
                dilution_format: 'Invalid dilution format'
            }
        },
        templateModal: {
            title: {
                new: 'New Template',
                edit: 'Edit Template',
                save: 'Save Template',
                load: 'Load Template'
            },
            fields: {
                name: {
                    label: 'Template Name',
                    placeholder: 'Enter template name'
                },
                description: {
                    label: 'Description',
                    placeholder: 'Enter template description'
                },
                category: {
                    label: 'Category',
                    placeholder: 'Select template category'
                },
                type: {
                    label: 'Detection Type',
                    options: {
                        qualitative: 'Qualitative',
                        quantitative: 'Quantitative',
                        semi_quantitative: 'Semi-quantitative'
                    }
                }
            },
            list: {
                title: 'Template List',
                empty: 'No templates found',
                search: 'Search templates',
                filter: 'Filter by category'
            },
            actions: {
                create: 'Create Template',
                save: 'Save Template',
                delete: 'Delete Template',
                load: 'Load Template',
                cancel: 'Cancel'
            },
            messages: {
                save_success: 'Template saved successfully',
                save_error: 'Failed to save template',
                delete_confirm: 'Are you sure you want to delete this template?',
                delete_success: 'Template deleted successfully',
                delete_error: 'Failed to delete template',
                load_error: 'Failed to load template'
            },
            validation: {
                name_required: 'Template name is required',
                name_exists: 'Template name already exists',
                category_required: 'Category is required',
                type_required: 'Detection type is required'
            }
        },
        detectionForm: {
            title: 'Detection Settings'
        },
        infoCard: {
            device: 'Device Information',
            project: 'Project Information',
            status: 'Status',
            serialNumber: 'Serial Number'
        },
        projectInfoForm: {
            // 项目信息表单标题
            title: 'Project Information',
            // 布局类型选项
            layoutType: {
                singleProject: 'Single Project Layout', // 单项目布局
                multiProjectHorizontal: 'Multi-Project Horizontal Layout', // 多项目水平布局
                multiProjectVertical: 'Multi-Project Vertical Layout' // 多项目垂直布局
            },
            // 共用空白选项
            useCommonBlank: 'Use Common Blank',
            // 项目选择提示
            selectProject: 'Please select a project',
            // 表单字段
            fields: {
                project_name: {
                    label: 'Project Name', // 项目名称
                    placeholder: 'Enter project name' // 请输入项目名称
                },
                project_code: {
                    label: 'Project Code', // 项目代码
                    placeholder: 'Enter project code' // 请输入项目代码
                },
                test_wavelength: {
                    label: 'Test Wavelength', // 测试波长
                    unit: 'nm' // 纳米
                },
                ref_wavelength: {
                    label: 'Reference Wavelength', // 参考波长
                    unit: 'nm' // 纳米
                },
                positive_judgment: 'Positive Judgment', // 阳性判断
                description: {
                    label: 'Description', // 项目描述
                    placeholder: 'Enter project description' // 请输入项目描述
                },
                operator: {
                    label: 'Operator', // 操作员
                    placeholder: 'Enter operator name' // 请输入操作员姓名
                },
                date: {
                    label: 'Test Date', // 检测日期
                    placeholder: 'Select test date' // 请选择检测日期
                },
                department: {
                    label: 'Department', // 部门
                    placeholder: 'Select department' // 请选择部门
                }
            },
            // 表单验证消息
            validation: {
                project_name: {
                    required: 'Project name is required', // 项目名称为必填项
                    min_length: 'Project name must be at least {{min}} characters', // 项目名称至少需要 {{min}} 个字符
                    max_length: 'Project name cannot exceed {{max}} characters' // 项目名称不能超过 {{max}} 个字符
                },
                project_code: {
                    required: 'Project code is required', // 项目代码为必填项
                    format: 'Invalid project code format' // 项目代码格式无效
                },
                operator: {
                    required: 'Operator name is required' // 操作员姓名为必填项
                },
                date: {
                    required: 'Test date is required', // 检测日期为必填项
                    invalid: 'Invalid date' // 日期格式无效
                }
            },
            // 提示消息
            toast: {
                loadFailed: {
                    title: 'Failed to load project list', // 加载项目列表失败
                    description: 'Database operation failed', // 数据库操作失败
                    systemError: 'System error, please try again' // 系统错误，请重试
                }
            },
            // 控制台消息
            messages: {
                clearProjectSelection: 'Clear project selection', // 清空项目选择
                selectProject: 'Select detection project:' // 选择检测项目：
            }
        },
        ReportPara: {
            title: 'Report Para.',

            reportMainTitile: 'Main Title',
            reportMainTitile_placeholder: 'Enter report main title',
            reportSubTitile: 'Sub Title',
            reportSubTitile_placeholder: 'Enter report sub title',
            testMethod: 'Test Method',
            testMethod_placeholder: 'Enter test method',
            testBasis: 'Test Basis',
            testBasis_placeholder: 'Enter test basis',
            testOperator: 'Test Operator',
            testOperator_placeholder: 'Enter test operator name',
            reviewer: 'Reviewer',
            reviewer_placeholder: 'Enter reviewer name',
            toast: {
                success: {
                    title: 'Save Successful',
                    description: 'Report parameters have been updated'
                },
                error: {
                    title: 'Save Failed',
                    description: 'Error occurred while saving report parameters'
                }
            }
        },
        reagentSupplier: {
            title: 'Reagent Supplier',
            placeholder: 'Enter supplier name',
            toast: {
                success: {
                    title: 'Save Successful',
                    description: 'Supplier list has been updated'
                },
                error: {
                    title: 'Save Failed',
                    description: 'Error occurred while saving supplier list'
                }
            }
        },
        lisPara: {
            title: 'LIS Para.',
            defaultPath: 'LIS File Default Save Path',
            selectPath: 'Select LIS Save Path',
            placeholder: 'Please select LIS file save path',
            toast: {
                success: {
                    title: 'Save Successful',
                    description: 'LIS file save path has been updated'
                },
                error: {
                    title: 'Save Failed',
                    description: 'Error occurred while saving LIS path'
                },
                noSelection: {
                    title: 'No Path Selected',
                    description: 'Please select a path for LIS file saving'
                }
            }
        },
        formulaEditor: {
            title: 'Formula Editor',
            validation: {
                correct: 'Formula format is correct',
                error: 'Formula syntax error',
                success: 'Formula syntax is correct',
                testSuccess: 'Formula test successful',
                testFailed: 'Formula test failed',
                enterFormula: 'Please enter a formula first',
                syntaxError: 'Syntax error:',
                unknownError: 'Unknown error',
                missingOperator: 'Missing operator between functions',
                functionUsageTips:
                    'Syntax error: Statistical function usage tips:\n• Single array: max(NC1) or min(NC1)\n• Compare means: max(mean(NC1), mean(NC2))\n• All values: max(flatten([NC1, NC2]))\nNot supported: max(NC1, NC2) and other multi-array parameters'
            },
            buttons: {
                validate: 'Validate Formula',
                test: 'Test Formula'
            },
            panels: {
                functions: 'Functions and Operators',
                variables: 'Variables'
            },
            cursorPosition: 'Position',
            testResult: 'Test Result',
            closeResult: 'Click to close test result',
            messages: {
                calculationResult: 'Calculation result:',
                simulationData: 'Simulation data:',
                formula: 'Formula:',
                functionAnd: 'function',
                and: 'and'
            },
            functions: {
                mean: {
                    description: 'Mean function',
                    category: 'Statistical functions'
                },
                max: {
                    description: 'Maximum function',
                    category: 'Statistical functions'
                },
                min: {
                    description: 'Minimum function',
                    category: 'Statistical functions'
                },
                sum: {
                    description: 'Sum function',
                    category: 'Statistical functions'
                }
            }
        }
    },

    // Page translations
    pages: {
        home: {
            infoCard: {
                device: {
                    title: 'Device Management',
                    description:
                        'Default connection device management, device connection parameter configuration...'
                },
                project: {
                    title: 'Project Management',
                    description:
                        'Detection project management (add, modify, delete), detection parameter configuration, data analysis method editing...'
                },
                report: {
                    title: 'Report Management',
                    description: 'Customize detection report, edit print information, export LIS...'
                }
            }
        },
        elisaControl: {
            title: 'ELISA Control',

            messages: {
                error: 'Error',
                success: 'Success',
                warning: 'Warning',
                info: 'Info',
                template: {
                    nameRequired: 'Please enter template name',
                    saveSuccess: 'Template saved successfully',
                    saveFailed: 'Failed to save template',
                    saveError: 'Error occurred while saving template',
                    loadSuccess: 'Template loaded successfully',
                    loadFailed: 'Failed to load template',
                    loadError: 'Error occurred while loading template',
                    deleteSuccess: 'Template deleted successfully',
                    deleteFailed: 'Failed to delete template',
                    deleteError: 'Error occurred while deleting template',
                    selectFirst: 'Please select a template to delete first',
                    listLoadFailed: 'Failed to load template list',
                    listLoadError: 'Error occurred while loading template list'
                },
                serial: {
                    configFailed: 'Failed to get serial port configuration',
                    configRequired: 'Please configure serial port parameters first',
                    openFailed: 'Failed to open serial port',
                    opened: 'Serial port opened, sending read command...',
                    sendFailed: 'Failed to send read command',
                    receiving: 'Receiving data...',
                    dataIncomplete:
                        'Received data is incomplete, may be missing some ELISA plate data',
                    noData: 'No data received',
                    readError: 'Error occurred during plate reading'
                },
                plate: {
                    readComplete: 'Plate reading completed',
                    dataSaved: 'Data has been saved, please check the results page for data...',
                    saveFailed: 'Failed to save plate reading data'
                }
            },
            buttons: {
                readPlate: 'Read Plate',
                reading: 'Reading...',
                clearTemplate: 'Clear Template',
                saveAsTemplate: 'Save As Template',
                selectTemplate: 'Select Template',
                deleteTemplate: 'Delete Template',
                deleteSelectedTemplate: 'Delete selected template'
            },
            dialogs: {
                deleteTemplate: {
                    title: 'Confirm Delete Template',
                    description:
                        'After deleting the template, all configuration information of this template will be permanently cleared. This operation cannot be undone.',
                    confirmText: 'Confirm Delete',
                    cancelText: 'Cancel'
                }
            }
        },
        resultData: {
            title: 'Detection Results',
            filter: {
                title: 'Filter Results',
                date_range: {
                    label: 'Date Range',
                    start: 'Start Date',
                    end: 'End Date'
                },
                project: {
                    label: 'Project',
                    placeholder: 'Select project'
                },
                operator: {
                    label: 'Operator',
                    placeholder: 'Select operator'
                },
                status: {
                    label: 'Status',
                    all: 'All',
                    completed: 'Completed',
                    processing: 'Processing',
                    error: 'Error'
                }
            },
            table: {
                columns: {
                    id: 'ID',
                    project_name: 'Project Name',
                    date: 'Test Date',
                    operator: 'Operator',
                    status: 'Status',
                    result: 'Result',
                    actions: 'Actions'
                },
                empty: 'No results found',
                loading: 'Loading results...'
            },
            details: {
                title: 'Result Details',
                export: 'Export Results',
                print: 'Print Report',
                delete: 'Delete Result'
            },
            chart: {
                title: 'Result Analysis',
                concentration: 'Concentration',
                absorbance: 'Absorbance',
                standard_curve: 'Standard Curve',
                sample_points: 'Sample Points'
            }
        },
        deviceManager: {
            title: 'Device Manager',
            connection: {
                title: 'Connection Settings',
                port: {
                    label: 'Serial Port',
                    placeholder: 'Select serial port'
                },
                baudRate: {
                    label: 'Baud Rate',
                    placeholder: 'Select baud rate'
                },
                status: {
                    connected: 'Connected',
                    disconnected: 'Disconnected',
                    connecting: 'Connecting...',
                    error: 'Connection Error'
                },
                buttons: {
                    connect: 'Connect',
                    disconnect: 'Disconnect',
                    refresh: 'Refresh Ports'
                }
            },
            control: {
                title: 'Device Control',
                temperature: {
                    label: 'Temperature Control',
                    current: 'Current: {{value}}°C',
                    target: 'Target: {{value}}°C',
                    set: 'Set Temperature'
                },
                motor: {
                    label: 'Motor Control',
                    position: 'Position: {{value}}',
                    speed: 'Speed: {{value}}',
                    home: 'Home Position',
                    move: 'Move To Position'
                },
                lamp: {
                    label: 'Lamp Control',
                    on: 'Turn On',
                    off: 'Turn Off',
                    status: 'Status: {{status}}'
                }
            },
            calibration: {
                title: 'Device Calibration',
                wavelength: {
                    label: 'Wavelength Calibration',
                    start: 'Start Calibration',
                    status: 'Status: {{status}}'
                },
                position: {
                    label: 'Position Calibration',
                    start: 'Start Calibration',
                    status: 'Status: {{status}}'
                }
            },
            maintenance: {
                title: 'Maintenance',
                last_calibration: 'Last Calibration: {{date}}',
                next_calibration: 'Next Calibration: {{date}}',
                lamp_hours: 'Lamp Hours: {{hours}}',
                system_check: 'System Check',
                self_test: 'Self Test'
            }
        },
        projectManager: {
            title: 'Project Manager',
            search_placeholder: 'Search [Project Name/Code]',
            isLoadingProjects: 'Loading projects...',
            emptyProjects: 'No matching projects found',
            label_deletePro: 'Delete Project',
            label_addPro: 'Add Project',
            type: 'Type',
            testWave: 'TestWave',
            refWave: 'RefWave',
            placeholder: 'Required, unique',
            placeholder_unique: 'Unique',
            placeholder_required: 'Required',
            pleaseSelect: 'Please select or add a project',

            label: {
                title: 'Project Para.',
                ProjectName: 'Project Name',
                ProjectCode: 'Project Code',
                resultShow: 'Result Show',
                resultUnit: 'Result Unit',
                refDown: 'Reference Down',
                refUp: 'Reference Up',
                refRange: 'Reference Range'
            },
            para: {
                title: 'Test Parameters',
                projectType: 'Project Type',
                blank: 'Blank Correction',
                xAxis: 'X Axis',
                yAxis: 'Y Axis',
                enteryMode: 'Entry Mode',
                shakeTime: 'Shake Time',
                negativeControl: 'Negative Control',
                positiveControl: 'Positive Control',
                rightTxtUp: '(Upper)',
                rightTxtDown: '(Lower)',
                rightTxtSec: '(Sec.)',
                cutoffFormula: 'Cutoff =',
                positiveJudge: 'Positive Judgment',
                grayZone: 'Enable Gray Zone',
                grayZoneDown: 'Gray Zone Lower Limit',
                grayZoneUp: 'Gray Zone Upper Limit'
            },
            toast: {
                success: {
                    title: 'Save Successful',
                    title_delete: 'Delete Successful',
                    description: 'Project parameters have been updated',
                    description_delete: 'Project has been deleted'
                },
                error: {
                    title: 'Save Failed',
                    title_input: 'Input Error',
                    title_delete: 'Delete Failed',
                    title_notEmpty: 'Project name and code cannot be empty',
                    title_projectList: 'Failed to load project list',
                    title_device: 'Failed to load device configuration',
                    description_system: 'System error, please try again',
                    description_db: 'Database operation failed, please try again',
                    description_exist: 'Already exists, please try again',
                    description_input: '[Lower] cannot be greater than [Upper] value'
                }
            }
        },
        ReportManager: {}
    }
} as const;

export default enUS;
