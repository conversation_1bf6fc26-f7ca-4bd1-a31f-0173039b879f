// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// 项目管理表 - 简化版，展开所有范围字段
model Project {
  id      String @id @default(cuid())
  name    String @unique // 项目名称，唯一约束
  code    String @unique // 项目代码，唯一约束
  version Int    @default(1) // 版本号，用于数据结构扩展和兼容性管理

  // 结果显示设置
  resultShow   Int // 0=数值结果 1=文本结果 2=阴阳性结果
  resultUnit   String // 结果单位
  refRangeText String // 参考范围显示文本

  // 测试参数
  testType           Int // 0=定性 1=定量
  testWave           Int // 检测波长滤光片编号 (0-8)
  refWave            Int // 参考波长滤光片编号 (0-8)
  useBlankCorrection Int // 空白校正 0=不使用 1=max 2=min 3=mean
  enteryMode         Int // 进板模式 0=连续 1=步进
  shakeTime          Int // 振板时间(秒)

  // 参考范围 (展开为独立字段)
  refRangeDown Float // 参考范围下限
  refRangeUp   Float // 参考范围上限

  // 阴性对照范围 (展开为独立字段)  
  ncRangeDown Float // 阴性对照范围下限
  ncRangeUp   Float // 阴性对照范围上限

  // 阳性对照范围 (展开为独立字段)
  pcRangeDown Float // 阳性对照范围下限
  pcRangeUp   Float // 阳性对照范围上限

  // 灰区范围 (展开为独立字段)
  grayRangeDown Float // 灰区范围下限
  grayRangeUp   Float // 灰区范围上限

  // 其他参数
  cutOffFormula     String // Cutoff计算公式
  postiveJudge      String // 阳性公式类型 >,>=, =<,<
  grayEnble         Boolean @default(false) // 是否启用灰区
  quantitativexAxis Int     @default(0) // 定量x轴类型 0=Linear 1=Log 2=Ln
  quantitativeyAxis Int     @default(0) // 定量y轴类型 0=Linear 1=Log 2=Ln

  // 系统字段
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("projects") // 表名映射
}

// 酶标板模板表
model PlateTemplate {
  id        String @id @default(cuid())
  name      String // 模板名称
  plateData String // 序列化的plateData (JSON字符串)

  createdBy String? // 创建者
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("plate_templates") // 表名映射
}

// 检测记录表
model TestRecord {
  id         String   @id @default(cuid()) // 检测记录ID
  mtpNumber  String // 酶标板编号
  testDate   DateTime @default(now()) // 检测日期
  updateDate DateTime @updatedAt // 更新日期

  // 复杂对象以JSON字符串形式存储
  testProjectJson        String // 检测项目JSON字符串
  testAdditionalInfoJson String // 检测附加信息JSON字符串
  wellDataJson           String? // 孔位数据JSON字符串

  // 基本类型字段直接存储
  cutOffValue Float? // 截断值（数值类型）

  @@map("test_records") // 表名映射
}
