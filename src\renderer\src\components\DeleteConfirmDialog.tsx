import React from 'react';
import {
    AlertDialog,
    AlertDialogOverlay,
    AlertDialogContent,
    AlertDialogHeader,
    AlertDialogBody,
    AlertDialogFooter,
    Button,
    Text
} from '@chakra-ui/react';

export interface DeleteConfirmDialogProps {
    /** 是否显示对话框 */
    isOpen: boolean;
    /** 关闭对话框的回调 */
    onClose: () => void;
    /** 确认删除的回调 */
    onConfirm: () => void;
    /** 要删除的项目名称 */
    itemName: string;
    /** 要删除的项目标识（可选，如代码、ID等） */
    itemIdentifier?: string;
    /** 对话框标题（可选，默认为"确认删除"） */
    title?: string;
    /** 自定义描述文本（可选） */
    description?: string;
    /** 确认按钮文字（可选，默认为"确认删除"） */
    confirmText?: string;
    /** 取消按钮文字（可选，默认为"取消"） */
    cancelText?: string;
    /** 是否正在执行删除操作 */
    isLoading?: boolean;
}

const DeleteConfirmDialog: React.FC<DeleteConfirmDialogProps> = ({
    isOpen,
    onClose,
    onConfirm,
    itemName,
    itemIdentifier,
    title = '确认删除',
    description,
    confirmText = '确认删除',
    cancelText = '取消',
    isLoading = false
}) => {
    const cancelRef = React.useRef<HTMLButtonElement>(null);

    const handleConfirm = () => {
        onConfirm();
    };

    const defaultDescription = itemIdentifier
        ? `您确定要删除 "${itemName}" (${itemIdentifier}) 吗？`
        : `您确定要删除 "${itemName}" 吗？`;

    return (
        <AlertDialog isOpen={isOpen} leastDestructiveRef={cancelRef} onClose={onClose} isCentered>
            <AlertDialogOverlay>
                <AlertDialogContent>
                    <AlertDialogHeader fontSize="lg" fontWeight="bold">
                        {title}
                    </AlertDialogHeader>

                    <AlertDialogBody>
                        <Text>{description || defaultDescription}</Text>
                        <Text fontSize="sm" color="gray.500" mt={2}>
                            此操作不可撤销，请谨慎操作。
                        </Text>
                    </AlertDialogBody>

                    <AlertDialogFooter>
                        <Button ref={cancelRef} onClick={onClose} disabled={isLoading}>
                            {cancelText}
                        </Button>
                        <Button
                            colorScheme="red"
                            onClick={handleConfirm}
                            ml={3}
                            isLoading={isLoading}
                            loadingText="删除中..."
                        >
                            {confirmText}
                        </Button>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialogOverlay>
        </AlertDialog>
    );
};

export default DeleteConfirmDialog;
