import { TestRecord, WellData } from '@shared/types/plateData';
import { Project } from '@shared/types/project';
import { SampleType } from '@shared/commondefines';
import { generateProjectTimestamp } from './dateUtils';

/**
 * 生成模拟的 Project 对象
 */
export const createMockProject = (code: string, name: string): Project => {
    return {
        id: new Date().getTime().toString(),
        name: name,
        code: code,
        // 结果显示设置
        resultShow: 0, // 0=数值结果 1=文本结果 2=阴阳性结果
        resultUnit: 'OD', // 结果单位
        refRangeText: '0~5', // 参考范围显示文本

        // 测试参数
        testType: 0, // 0=定性 1=定量
        testWave: 0, // 检测波长滤光片编号 (0-8)
        refWave: 0, // 参考波长滤光片编号 (0-8)
        useBlankCorrection: 0, // 空白校正 0=不使用 1=max 2=min 3=mean
        enteryMode: 0, // 进板模式 0=连续 1=步进
        shakeTime: 0, // 振板时间(秒)

        // 参考范围 (展开为独立字段)
        refRangeDown: 0, // 参考范围下限
        refRangeUp: 5, // 参考范围上限

        // 阴性对照范围 (展开为独立字段)
        ncRangeDown: 0, // 阴性对照范围下限
        ncRangeUp: 5, // 阴性对照范围上限

        // 阳性对照范围 (展开为独立字段)
        pcRangeDown: 0, // 阳性对照范围下限
        pcRangeUp: 5, // 阳性对照范围上限

        // 灰区范围 (展开为独立字段)
        grayRangeDown: 0, // 灰区范围下限
        grayRangeUp: 5, // 灰区范围上限

        // 其他参数
        cutOffFormula: 'Cutoff计算公式', // Cutoff计算公式
        postiveJudge: '>=', // 阳性公式类型 >,>=, =<,<
        grayEnble: false, // 是否启用灰区
        quantitativexAxis: 0, // 定量x轴类型 0=Linear 1=Log 2=Ln
        quantitativeyAxis: 0 // 定量y轴类型 0=Linear 1=Log 2=Ln
    };
};

/**
 * 生成模拟的 WellData 对象
 */
export const createMockWellData = (
    sampleType: SampleType,
    sampleNumber: string,
    odValue?: number
): WellData => {
    return {
        sampleType: {
            name: sampleType,
            type: sampleType
        },
        sampleNumber,
        odMain: odValue || Math.random() * 2,
        odRef: odValue || Math.random() * 2,
        odValue: odValue || Math.random() * 2,
        odRatio: odValue ? odValue / 0.1 : Math.random() * 10,
        concentration: odValue ? odValue / 0.1 : Math.random() * 10,
        result: 0
    };
};

/**
 * 生成模拟的孔位数据
 */
export const createMockWellDataMap = (): { [wellId: string]: WellData } => {
    const wellData: { [wellId: string]: WellData } = {};

    // 生成96孔板的数据 (8行 x 12列)
    const rows = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H'];
    const cols = Array.from({ length: 12 }, (_, i) => i + 1);

    rows.forEach((row, rowIndex) => {
        cols.forEach((col) => {
            const wellId = `${row}${col}`;

            // 根据位置分配不同的样本类型
            let sampleType: SampleType;
            let sampleNumber: string;

            if (rowIndex === 0 && col <= 2) {
                // A1, A2 为空白孔
                sampleType = '';
                sampleNumber = '';
            } else if (rowIndex === 1 && col <= 7) {
                // B1-B7 为标准孔
                sampleType = 'std';
                sampleNumber = `STD${col}`;
            } else if (rowIndex === 2 && col <= 2) {
                // C1, C2 为质控孔
                sampleType = 'qc';
                sampleNumber = `QC${col}`;
            } else {
                // 其他为样本孔
                sampleType = 'sample';
                sampleNumber = `S${(rowIndex - 3) * 12 + col}`;
            }

            wellData[wellId] = createMockWellData(sampleType, sampleNumber);
        });
    });

    return wellData;
};

/**
 * 生成单个 TestRecord 对象
 */
export const createMockTestRecord = (
    id: string,
    date: string,
    projectCode: string,
    projectName: string,
    options?: {
        mainTitle?: string;
        subTitle?: string;
        note?: string;
        testMethod?: string;
        testOperator?: string;
        reagentBatch?: string;
        reagentExpiry?: string;
        reagentSupplier?: string;
        testTemperature?: number;
        testRelativeHumidity?: number;
        testReviewer?: string;
        cutOffValue?: number;
        includeWellData?: boolean;
        sequenceNumber?: number; // 添加序号参数
    }
): TestRecord => {
    // 使用记录日期生成固定的 mtpNumber，而不是当前时间
    const recordDate = new Date(date);
    // 设置固定的时间（比如上午10点），确保同一天的所有记录有相同的时间戳
    recordDate.setHours(10, 0, 0, 0);
    let mtpNumber = generateProjectTimestamp(projectCode, recordDate);

    // 如果有序号，添加到 mtpNumber 后面确保唯一性
    if (options?.sequenceNumber !== undefined) {
        mtpNumber = `${mtpNumber}_${String(options.sequenceNumber).padStart(3, '0')}`;
    }

    return {
        id,
        mtpNumber,
        testDate: new Date(),
        updateDate: new Date(),
        cutOffValue: options?.cutOffValue || 0.1,
        testProject: createMockProject(projectCode, projectName),
        testAdditionalInfo: {
            reagentBatch: options?.reagentBatch || `BATCH${Math.floor(Math.random() * 1000)}`,
            reagentExpiry: options?.reagentExpiry || '2025-12-31',
            reagentSupplier: options?.reagentSupplier || '供应商A',
            testTemperature: options?.testTemperature || 25 + Math.random() * 5,
            testRelativeHumidity: options?.testRelativeHumidity || 50 + Math.random() * 20,
            testReviewer: options?.testReviewer || '审核员A',
            mainTitle: options?.mainTitle || '主标题',
            subTitle: options?.subTitle || '副标题',
            note: options?.note || '备注',
            testMethod: options?.testMethod || '检测方法',
            testOperator: options?.testOperator || '操作员A'
        },
        wellData: options?.includeWellData !== false ? createMockWellDataMap() : undefined
    };
};

/**
 * 生成一组 TestRecord 测试数据
 */
export const createMockTestRecords = (count: number = 10): TestRecord[] => {
    const testRecords: TestRecord[] = [];
    const projects = [
        { code: 'PROJ001', name: '项目A' },
        { code: 'PROJ002', name: '项目B' },
        { code: 'PROJ003', name: '项目C' },
        { code: 'PROJ004', name: '项目D' },
        { code: 'PROJ005', name: '项目E' }
    ];

    const baseDate = new Date('2024-03-15');

    for (let i = 0; i < count; i++) {
        const projectIndex = i % projects.length;
        const project = projects[projectIndex];

        // 每3个记录使用相同日期
        const dateIndex = Math.floor(i / 3);
        const recordDate = new Date(baseDate);
        recordDate.setDate(baseDate.getDate() + dateIndex);

        const testRecord = createMockTestRecord(
            `record_${i + 1}`,
            recordDate.toISOString().split('T')[0], // YYYY-MM-DD 格式
            project.code,
            project.name,
            {
                includeWellData: true, // 包含孔位数据
                sequenceNumber: i + 1 // 添加序号确保唯一性
            }
        );

        testRecords.push(testRecord);
    }

    return testRecords;
};

/**
 * 生成简化的测试数据（不包含孔位数据）
 */
export const createSimpleMockTestRecords = (count: number = 10): TestRecord[] => {
    const testRecords: TestRecord[] = [];
    const projects = [
        { code: 'PROJ001', name: '项目A' },
        { code: 'PROJ002', name: '项目B' },
        { code: 'PROJ003', name: '项目C' }
    ];

    const baseDate = new Date('2024-03-15');

    for (let i = 0; i < count; i++) {
        const projectIndex = i % projects.length;
        const project = projects[projectIndex];

        // 每3个记录使用相同日期
        const dateIndex = Math.floor(i / 3);
        const recordDate = new Date(baseDate);
        recordDate.setDate(baseDate.getDate() + dateIndex);

        const testRecord = createMockTestRecord(
            `record_${i + 1}`,
            recordDate.toISOString().split('T')[0],
            project.code,
            project.name,
            {
                includeWellData: false, // 不包含孔位数据
                sequenceNumber: i + 1 // 添加序号确保唯一性
            }
        );

        testRecords.push(testRecord);
    }

    return testRecords;
};
