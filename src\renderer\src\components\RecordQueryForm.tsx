import React, { useState } from 'react';
import { Box, Flex, FormControl, FormLabel, Input, Button, HStack, VStack } from '@chakra-ui/react';

interface RecordQueryFormProps {
    onQuery: (params: { startDate: string; endDate: string; mtpNumber: string }) => void;
}

const RecordQueryForm: React.FC<RecordQueryFormProps> = ({ onQuery }) => {
    const [startDate, setStartDate] = useState('');
    const [endDate, setEndDate] = useState('');
    const [mtpNumber, setMtpNumber] = useState('');

    const handleQuery = () => {
        onQuery({
            startDate,
            endDate,
            mtpNumber
        });
    };

    const handleReset = () => {
        setStartDate('');
        setEndDate('');
        setMtpNumber('');
        // 统一使用 handleQuery 的空条件分支
        onQuery({
            startDate: '',
            endDate: '',
            mtpNumber: ''
        });
    };

    return (
        <Box p={2} bg="white" border="1px" borderColor="gray.200" borderRadius="md" mb={1}>
            {/* <Text fontSize="md" fontFamily="MiSans-Bold" mb={3}>
                查询条件
            </Text>
            <Divider mb={3} /> */}

            <VStack spacing={3} align="stretch">
                <HStack spacing={1}>
                    <FormControl>
                        <FormLabel fontSize="sm" fontFamily="MiSans-Normal" mb={1}>
                            起始日期
                        </FormLabel>
                        <Input
                            type="date"
                            value={startDate}
                            onChange={(e) => setStartDate(e.target.value)}
                            size="sm"
                            fontFamily="MiSans-Normal"
                        />
                    </FormControl>

                    <FormControl>
                        <FormLabel fontSize="sm" fontFamily="MiSans-Normal" mb={1}>
                            终止日期
                        </FormLabel>
                        <Input
                            type="date"
                            value={endDate}
                            onChange={(e) => setEndDate(e.target.value)}
                            size="sm"
                            fontFamily="MiSans-Normal"
                        />
                    </FormControl>
                </HStack>

                <FormControl>
                    <HStack spacing={1} justify="space-between">
                        <FormLabel
                            // flexWrap={'nowrap'}
                            fontSize="sm"
                            fontFamily="MiSans-Normal"
                            m={0}
                            p={0}
                            whiteSpace="nowrap"
                            overflow="hidden"
                            textOverflow="ellipsis"
                            minW="fit-content"
                        >
                            微板编号
                        </FormLabel>
                        <Input
                            placeholder="微板编号（支持模糊查询，如：20241202、%20241202%）"
                            value={mtpNumber}
                            onChange={(e) => setMtpNumber(e.target.value)}
                            size="sm"
                            fontFamily="MiSans-Normal"
                            title="支持模糊查询：输入部分字符或使用%通配符，如：20241202、%20241202%"
                        />

                        <Flex gap={1} justify="flex-end">
                            <Button
                                size="sm"
                                variant="ghost"
                                colorScheme="blue"
                                onClick={handleQuery}
                                fontFamily="MiSans-Normal"
                            >
                                查询
                            </Button>
                            <Button
                                size="sm"
                                variant="ghost"
                                colorScheme="blue"
                                onClick={handleReset}
                                fontFamily="MiSans-Normal"
                            >
                                重置
                            </Button>
                        </Flex>
                    </HStack>
                </FormControl>
            </VStack>
        </Box>
    );
};

export default RecordQueryForm;
