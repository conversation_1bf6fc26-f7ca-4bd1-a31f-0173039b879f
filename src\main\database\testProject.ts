/**
 * 极简版项目数据库操作
 * 直接使用 Prisma 类型，无需任何转换
 */

import { ipcMain } from 'electron';
import { IPCChannels } from '@shared/ipcChannels';
import { prisma, handlePrismaError } from './index';
import type { Project, ApiResponse } from '@shared/types';

// ==================== CRUD 操作 ====================

// 获取项目列表
export async function getProjectList(): Promise<ApiResponse<Project[]>> {
    try {
        const projects = await prisma.project.findMany({
            orderBy: { createdAt: 'desc' }
        });
        // 无需任何转换！直接返回
        return { success: true, data: projects };
    } catch (error: unknown) {
        return handlePrismaError<Project[]>(error, '获取项目列表');
    }
}

// 添加项目
export async function addProject(
    project: Omit<Project, 'id' | 'version' | 'createdAt' | 'updatedAt'>
): Promise<ApiResponse<Project>> {
    try {
        const result = await prisma.project.create({
            data: {
                ...project,
                version: 1
            }
        });
        // 无需任何转换！直接返回
        return { success: true, data: result };
    } catch (error: unknown) {
        return handlePrismaError<Project>(error, '添加项目');
    }
}

// 删除项目
export async function deleteProject(id: string): Promise<ApiResponse> {
    try {
        await prisma.project.delete({ where: { id } });
        return { success: true };
    } catch (error: unknown) {
        return handlePrismaError(error, '删除项目');
    }
}

// 更新项目
export async function updateProject(project: Project): Promise<ApiResponse<Project>> {
    try {
        const result = await prisma.project.update({
            where: { id: project.id },
            data: {
                ...project,
                updatedAt: new Date()
            }
        });
        // 无需任何转换！直接返回
        return { success: true, data: result };
    } catch (error: unknown) {
        return handlePrismaError<Project>(error, '更新项目');
    }
}

// IPC 处理器设置
export function setupTestProjectHandlers(): void {
    ipcMain.handle(IPCChannels.configInfo.GetProjectList, () => getProjectList());
    ipcMain.handle(IPCChannels.configInfo.AddProject, (_, project) => addProject(project));
    ipcMain.handle(IPCChannels.configInfo.DeleteProject, (_, id) => deleteProject(id));
    ipcMain.handle(IPCChannels.configInfo.UpdateProject, (_, project: Project) =>
        updateProject(project)
    );
}
