import React from 'react';
import {
    <PERSON><PERSON>,
    <PERSON>dal<PERSON>verlay,
    Modal<PERSON>ontent,
    Modal<PERSON>eader,
    Modal<PERSON>ooter,
    ModalBody,
    Tabs,
    TabList,
    TabPanels,
    TabPanel,
    Tab,
    Button,
    HStack,
    Icon,
    useToast
} from '@chakra-ui/react';
import { InfoIcon } from '@chakra-ui/icons';
import { useTranslation } from 'react-i18next';

import ReportPara from '@components/report/ReportPara';
import ReagentSupplier from '@components/report/ReagentSupplier';
import LisPara from '@components/report/LisPara';
import logger from '@renderer/utils/logger';

interface ReportManagerProps {
    isOpen: boolean;
    onClose: () => void;
}

const ReportManager: React.FC<ReportManagerProps> = ({ isOpen, onClose }) => {
    const toast = useToast();
    // const deviceSettings = useDeviceSettings(isOpen);
    // const serialConfig = useSerialConfig(isOpen);
    // const filterManager = useFilterManager(deviceSettings.selectedDevice);

    const { t } = useTranslation(['common', 'pages']);

    const handleConfirm = async (): Promise<void> => {
        try {
            // if (deviceSettings.selectedDevice === UNSET_DEVICE.model) {
            //     toast({
            //         title: '请选择设备型号',
            //         status: 'warning',
            //         duration: 3000,
            //         isClosable: true
            //     });
            //     return;
            // }

            // // 保存所有配置
            // await window.customApi.app.updateAppSettings({
            //     deviceModel: deviceSettings.selectedDevice
            // });

            // if (!(await serialConfig.saveConfig())) {
            //     throw new Error('通讯参数保存失败');
            // }

            // if (!(await filterManager.saveFilters())) {
            //     throw new Error('滤光片参数保存失败');
            // }

            toast({
                title: t('common:message.saveSuccess'),
                status: 'success',
                duration: 2000
            });
            onClose();
        } catch (error) {
            logger.error('save config failed', error, {
                component: './src/renderer/src/pages/1_3_reportMgr/index.tsx'
            });

            toast({
                title: String(error),
                status: 'error',
                duration: 3000,
                isClosable: true
            });
        }
    };

    return (
        <>
            <Modal
                isOpen={isOpen}
                onClose={onClose}
                size="xl"
                isCentered
                closeOnEsc={false}
                closeOnOverlayClick={false}
            >
                <ModalOverlay />
                <ModalContent maxW="600px" maxH="650px" w="100vw" h="100vh" overflow="auto">
                    <ModalHeader fontFamily="MiSans-Bold" bg="teal.500" color="white">
                        <HStack>
                            <Icon as={InfoIcon} boxSize={6} />
                            {/* 报告管理*/}
                            <span> {t('pages:home.infoCard.report.title')}</span>
                        </HStack>
                    </ModalHeader>
                    <ModalBody>
                        <Tabs>
                            <TabList>
                                {/* 报告参数 */}
                                <Tab fontFamily="MiSans-Bold">
                                    {t('components:ReportPara.title')}
                                </Tab>
                                {/* 试剂供应商 */}
                                <Tab fontFamily="MiSans-Bold">
                                    {t('components:reagentSupplier.title')}
                                </Tab>
                                {/* LIS 参数 */}
                                <Tab fontFamily="MiSans-Bold">{t('components:lisPara.title')}</Tab>
                            </TabList>
                            <TabPanels>
                                <TabPanel>
                                    <ReportPara />
                                </TabPanel>
                                <TabPanel>
                                    <ReagentSupplier />
                                </TabPanel>
                                <TabPanel>
                                    <LisPara />
                                </TabPanel>
                            </TabPanels>
                        </Tabs>
                    </ModalBody>
                    <ModalFooter>
                        <Button colorScheme="blue" mr={3} onClick={handleConfirm} size={'md'}>
                            {/* 确定 */}
                            {t('common:button.confirm')}
                        </Button>
                        <Button onClick={onClose} size={'md'}>
                            {/* 取消 */}
                            {t('common:button.cancel')}
                        </Button>
                    </ModalFooter>
                </ModalContent>
            </Modal>
        </>
    );
};

export default ReportManager;
