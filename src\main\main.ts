import { app, shell, BrowserWindow } from 'electron';
import { electronApp, optimizer, is } from '@electron-toolkit/utils';
import path from 'path';
import icon from '../../resources/icon.png?asset';
import logger from './utils/logger';

// // 在开发环境中禁用安全警告
// if (is.dev) {
//     process.env.ELECTRON_DISABLE_SECURITY_WARNINGS = 'true';
// }

import { setupLogHandlers } from './log';
import { setupAppHandlers } from './app';
import { setupConfigInfoHandlers } from './configInfo';
import { setupStoreHandlers } from './store';
import { setupSerialHandlers } from './serial';
import { setupDatabaseHandlers, initializeDatabase, closeDatabase } from './database';
import { setupMtpTemplateHandlers } from './mtpTemplate';
import { setupPrintHandlers } from './print';

// 注意：在 Windows 系统下，如果控制台输出中文显示乱码，
// 请在启动应用前手动在命令行执行：chcp 65001

// 设置Windows控制台代码页为UTF-8
// 设置进程编码
// process.env.LANG = 'zh_CN.UTF-8';
// process.stdout.setDefaultEncoding('utf8');
// process.stderr.setDefaultEncoding('utf8');

// // 配置GPU相关选项
// app.commandLine.appendSwitch('enable-gpu-rasterization');
// app.commandLine.appendSwitch('enable-zero-copy');
// app.commandLine.appendSwitch('ignore-gpu-blocklist');
// app.commandLine.appendSwitch('enable-hardware-overlays', 'single-fullscreen,single-on-top');

// // 禁用一些不需要的GPU特性来提高稳定性
// app.disableHardwareAcceleration();
// app.commandLine.appendSwitch('disable-gpu-compositing');
// app.commandLine.appendSwitch('disable-gpu-memory-buffer-video-frames');

let splashWindow: BrowserWindow | null = null;
let mainWindow: BrowserWindow | null = null;

// 创建启动画面
function createSplashWindow(): BrowserWindow {
    splashWindow = new BrowserWindow({
        width: 400,
        height: 300,
        frame: false,
        resizable: false,
        alwaysOnTop: true,
        transparent: true,
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true
        }
    });
    // 优化启动画面HTML，添加更好的视觉效果
    const splashHtml = `
        <!DOCTYPE html>
        <html>
        <head>
            <style>
                body {
                    margin: 0;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                    height: 100vh;
                    font-family: 'Microsoft YaHei', Arial, sans-serif;
                    color: white;
                    overflow: hidden;
                }
                .logo { 
                    font-size: 28px; 
                    margin-bottom: 30px; 
                    font-weight: bold;
                    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
                }
                .loading { 
                    font-size: 14px; 
                    margin-top: 25px; 
                    opacity: 0.9;
                }
                .spinner {
                    border: 4px solid rgba(255,255,255,0.2);
                    border-top: 4px solid white;
                    border-radius: 50%;
                    width: 40px;
                    height: 40px;
                    animation: spin 1.2s linear infinite;
                }
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
                .progress {
                    width: 200px;
                    height: 4px;
                    background: rgba(255,255,255,0.2);
                    border-radius: 2px;
                    margin-top: 20px;
                    overflow: hidden;
                }
                .progress-bar {
                    height: 100%;
                    background: white;
                    border-radius: 2px;
                    animation: progress 3s ease-in-out infinite;
                }
                @keyframes progress {
                    0% { width: 0%; }
                    50% { width: 70%; }
                    100% { width: 100%; }
                }
            </style>
        </head>
        <body>
            <div class="logo">KHB ST ELISA</div>
            <div class="spinner"></div>
            <div class="loading">正在初始化系统...</div>
            <div class="progress">
                <div class="progress-bar"></div>
            </div>
        </body>
        </html>
    `;
    // // 加载启动画面HTML
    // const splashHtml = `
    //     <!DOCTYPE html>
    //     <html>
    //     <head>
    //         <style>
    //             body {
    //                 margin: 0;
    //                 background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    //                 display: flex;
    //                 flex-direction: column;
    //                 justify-content: center;
    //                 align-items: center;
    //                 height: 100vh;
    //                 font-family: Arial, sans-serif;
    //                 color: white;
    //             }
    //             .logo { font-size: 24px; margin-bottom: 20px; }
    //             .loading { font-size: 14px; margin-top: 20px; }
    //             .spinner {
    //                 border: 3px solid rgba(255,255,255,0.3);
    //                 border-top: 3px solid white;
    //                 border-radius: 50%;
    //                 width: 30px;
    //                 height: 30px;
    //                 animation: spin 1s linear infinite;
    //             }
    //             @keyframes spin {
    //                 0% { transform: rotate(0deg); }
    //                 100% { transform: rotate(360deg); }
    //             }
    //         </style>
    //     </head>
    //     <body>
    //         <div class="logo">KHB ST ELISA</div>
    //         <div class="spinner"></div>
    //         <div class="loading">正在启动应用程序...</div>
    //     </body>
    //     </html>
    // `;

    splashWindow.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(splashHtml)}`);
    splashWindow.center();
    splashWindow.show();

    return splashWindow;
}

// 创建主窗口
function createWindow(): void {
    mainWindow = new BrowserWindow({
        width: 1300,
        height: 960,
        minWidth: 1024,
        minHeight: 768,
        show: false,
        autoHideMenuBar: true,
        frame: true, // 保持窗口边框
        // transparent: true, // 设置窗口透明
        resizable: true,
        maximizable: true, // 明确启用最大化
        minimizable: true, // 明确启用最小化
        closable: true, // 明确启用关闭
        // backgroundColor: '#f5f5f5', // 添加背景色以减少白屏
        ...(process.platform === 'linux' ? { icon } : {}),
        webPreferences: {
            preload: path.join(__dirname, '../preload/preload.js'),
            sandbox: true,
            contextIsolation: true,
            nodeIntegration: false,
            devTools: is.dev,
            backgroundThrottling: false, // 禁用后台节流
            webSecurity: true, // 启用web安全
            allowRunningInsecureContent: false
        }
    });

    logger.info('Preload script path: ' + path.join(__dirname, '../preload/preload.js'), {
        component: './src/main/main.ts'
    });
    // 主窗口准备好后关闭启动画面
    mainWindow.once('ready-to-show', () => {
        // 延迟1000ms关闭启动画面，让主窗口内容完全渲染
        setTimeout(() => {
            if (splashWindow) {
                splashWindow.close();
                splashWindow = null;
            }
            mainWindow?.show();
        }, 1500);
    });

    // 监听页面加载完成事件
    mainWindow.webContents.once('did-finish-load', () => {
        // 页面加载完成后再延迟一点时间确保渲染完成
        setTimeout(() => {
            if (splashWindow) {
                splashWindow.close();
                splashWindow = null;
            }
            mainWindow?.show();
        }, 500);
    });

    // 安全处理外部链接
    mainWindow.webContents.setWindowOpenHandler((details) => {
        // 添加URL安全检查
        const url = details.url;

        // 只允许 https 和 http 协议
        if (url.startsWith('https://') || url.startsWith('http://')) {
            shell.openExternal(url);
        } else {
            logger.warn('阻止打开不安全的URL:', {
                data: { url },
                component: './src/main/main.ts'
            });
        }

        return { action: 'deny' };
    });
    // 加载页面
    if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
        mainWindow.loadURL(process.env['ELECTRON_RENDERER_URL']);
    } else {
        mainWindow.loadFile(path.join(__dirname, '../renderer/index.html'));
    }

    if (is.dev) {
        mainWindow.webContents.openDevTools({ mode: 'right' });
    }
}

// 应用初始化
app.whenReady().then(async () => {
    // 注册日志处理器
    setupLogHandlers();

    // 立即显示启动画面，不等待任何异步操作
    createSplashWindow();

    electronApp.setAppUserModelId('com.electron');

    // 快捷键监听
    app.on('browser-window-created', (_, window) => {
        optimizer.watchWindowShortcuts(window);
    });

    // 立即创建主窗口（但不显示）
    createWindow();

    // 初始化数据库
    try {
        await initializeDatabase();
        logger.info('database initialized successfully', { component: './src/main/main.ts' });
    } catch (error) {
        logger.error('database init error:', error, { component: './src/main/main.ts' });
    }

    setTimeout(() => {
        // 注册应用相关 IPC 处理程序
        setupAppHandlers();

        // 注册数据库相关 IPC 处理器
        setupDatabaseHandlers();

        // 注册存储相关的 IPC 处理程序
        setupStoreHandlers();

        // 注册设备相关
        setupConfigInfoHandlers();

        // 注册串口相关
        setupSerialHandlers();

        // 注册模板相关
        setupMtpTemplateHandlers();

        // 注册打印相关
        setupPrintHandlers();
    }, 2000);

    // macOS 下点击 Dock 图标重新创建窗口
    app.on('activate', () => {
        if (BrowserWindow.getAllWindows().length === 0) createWindow();
    });
});

// 关闭所有窗口时退出（macOS 除外）
app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

// 应用退出前释放数据库连接
app.on('before-quit', async (event) => {
    // 防止应用立即退出，等待数据库连接释放完成
    event.preventDefault();

    try {
        logger.info('开始释放数据库连接...', { component: './src/main/main.ts' });

        // 设置超时，避免应用卡死
        const disconnectPromise = closeDatabase();
        const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error('数据库断开连接超时')), 5000);
        });

        await Promise.race([disconnectPromise, timeoutPromise]);
        logger.info('数据库连接已成功释放', { component: './src/main/main.ts' });
    } catch (err) {
        logger.error('释放数据库连接失败', err, { component: './src/main/main.ts' });
    } finally {
        // 无论成功失败，都允许应用退出
        app.exit(0);
    }
});

// 处理强制退出的情况
app.on('will-quit', async () => {
    logger.info('应用即将退出，最后尝试释放数据库连接', { component: './src/main/main.ts' });

    // 快速尝试断开连接，不阻塞退出
    try {
        await Promise.race([
            closeDatabase(),
            new Promise((resolve) => setTimeout(resolve, 1000)) // 最多等1秒
        ]);
    } catch (err) {
        logger.error('强制退出时释放连接失败', err, { component: './src/main/main.ts' });
    }
});

// 捕获主进程未处理异常，避免崩溃
process.on('uncaughtException', (err) => {
    logger.error('主进程未捕获异常', err, { component: './src/main/main.ts' });
});
process.on('unhandledRejection', (reason) => {
    logger.error('主进程未处理的Promise异常', reason, { component: './src/main/main.ts' });
});
