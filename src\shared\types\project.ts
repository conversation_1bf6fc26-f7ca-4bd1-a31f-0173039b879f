// import {
//     AppSettings as PrismaAppSettings,
//     SerialConfig as PrismaSerialConfig,
//     FilterInfo as PrismaFilterInfo
// } from '@prisma/client';

// import { SerialPortOpenOptions } from 'serialport';

// // 保持和 Prisma 类型同步
// export type AppSettings = PrismaAppSettings;
// export type SerialConfig = PrismaSerialConfig;
// export type FilterInfo = PrismaFilterInfo;

// // 创建数据时使用的类型（不包含 id 和 updatedAt）
// export type AppSettingsData = Omit<PrismaAppSettings, 'id' | 'updatedAt'>;
// export type SerialConfigData = Omit<PrismaSerialConfig, 'id' | 'updatedAt'>;
// export type FilterInfoData = Omit<PrismaFilterInfo, 'id'>;
// export type FilterData = Omit<FilterInfoData, 'deviceModel'>;

// // 仅用于响应的接口类型
// export interface CreateFiltersResponse {
//     count: number;
// }

// 通道滤光片编号
export type FiltersNo = 0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8;
// 通道滤光片对象
export type Filters = { no: FiltersNo; wavelength: number };

// 设备配置信息原始数据
export interface DeviceConfigRaw {
    model: string;
    filters: Filters[];
}
// 设备配置信息
export interface DeviceConfig extends DeviceConfigRaw {
    name: string;
    type: string;
}

// 统一的 API 响应格式
export type ApiResponse<T = void> = {
    success: boolean;
    data?: T;
    error?: string;
};

/*------------------------------------串口-----------------------------------------------*/
// 数据位
export type DataBits = 5 | 6 | 7 | 8;
// 停止位
export type StopBits = 1 | 1.5 | 2;
// 校验位
export type Parity = 'none' | 'even' | 'odd' | 'mark' | 'space';

// 定义串口选项类型
// export type SerialPortOptions = Partial<Omit<SerialPortOpenOptions<any>, 'path'>>;
export interface SerialPortOptions {
    path?: string; // 串口路径
    baudRate?: number; // 波特率
    dataBits?: DataBits; // 数据位 (5, 6, 7, or 8)
    stopBits?: StopBits; // 停止位 (1, 1.5, or 2)
    parity?: Parity; // 校验位 (none, odd, even)
    timeout?: number; // 超时时间(秒)
}

// 定义串口信息类型
export interface PortsList {
    path: string; // 串口路径
}
/*------------------------------------串口  -----------------------------------------------*/

/*------------------------------------项目管理-----------------------------------------------*/
export type TestType = 0 | 1;
export const projectTestType = [
    { id: 0, label: '定性' },
    { id: 1, label: '定量' }
] as const;

export type axisType = 0 | 1 | 2;
export const quantitativeAxisType = [
    { id: 0, label: 'Linear' },
    { id: 1, label: 'Log{x}' },
    { id: 2, label: 'Ln{x}' }
] as const;

export type ResultShow = 0 | 1 | 2;
export const projectResultShow = [
    { id: 0, label: '数值结果' },
    { id: 1, label: '文本结果' },
    { id: 2, label: '阴/阳性结果' }
] as const;

export type BlankCorrection = 0 | 1 | 2 | 3;
export const projectBlankCorrection = [
    { id: 0, label: '不使用空白校正' },
    { id: 1, label: 'max(Blank)' },
    { id: 2, label: 'min(Blank)' },
    { id: 3, label: 'mean(Blank)' }
] as const;

export type EnteryMode = 0 | 1;
export const mtpEnteryMode = [
    { id: 0, label: '连续' },
    { id: 1, label: '步进' }
] as const;

export type PositiveJudge = '>' | '>=' | '<=' | '<';
export const projectPositiveJudge = [
    { id: '>', label: '>' },
    { id: '>=', label: '>=' },
    { id: '<=', label: '<=' },
    { id: '<', label: '<' }
] as const;

// 直接使用 Prisma 生成的类型 - 无需任何修改！
import type { Project as PrismaProject } from '@prisma/client';

// 重新导出 Prisma 类型，保持接口一致性
export type Project = Omit<PrismaProject, 'version' | 'createdAt' | 'updatedAt'>;

// 创建项目时使用的类型（不包含系统字段）
// export type ProjectUpdateInput = Partial<PrismaProject> & { id: string };

/*------------------------------------项目管理-----------------------------------------------*/
